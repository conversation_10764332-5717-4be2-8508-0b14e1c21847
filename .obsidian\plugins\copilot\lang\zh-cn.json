{"manifest": {"translationVersion": 1742967019686, "pluginVersion": "2.8.2"}, "description": {"original": "A ChatGPT Copilot in Obsidian.", "translation": "AI助手"}, "dict": {"Notice(\"No active leaf found.\")": "Notice(\"未找到激活的子项\")", "Notice(\"Failed to open a markdown view.\")": "Notice(\"无法打开Markdown视图\")", "Notice(\"Message inserted into the active note.\")": "Notice(\"消息已插入到激活的文档中。\")", "Notice(\n        \"Copilot Plus license key not found. Please enter your license key in the settings.\"\n      )": "Notice(\n        \"未找到 Copilot Plus 许可证密钥。请在设置中输入您的许可证密钥。\"\n      )", "Notice(errorMessage)": "Notice(errorMessage)", "Notice(`Error creating model: ${modelKey}`)": "Notice(`创建模型时出错:${modelKey}`)", "Notice(\n          \"Connection successful, but requires CORS to be enabled. Please enable CORS for this model once you add it above.\"\n        )": "Notice(\n          \"连接成功，但需要启用CORS。请在添加此模型后启用CORS。\"\n        )", "Notice(error.message)": "Notice(error.message)", "Notice(\"An error occurred while executing the tool. Check console for details.\")": "Notice(\"执行工具时发生错误。请检查控制台以获取详细信息。\")", "Notice(\"Failed to initialize Copilot database. Some features may be limited.\")": "Notice(\"无法初始化 Copilot 数据库。部分功能可能受限。\")", "Notice(\"Local Copilot index cleared successfully.\")": "Notice(\"本地 Copilot 索引已成功清除。\")", "Notice(\"An error occurred while clearing the local Copilot index.\")": "Notice(\"清除本地 Copilot 索引时发生错误。\")", "Notice(\"New embedding model detected. Rebuilding Copilot index from scratch.\")": "Notice(\"检测到新的嵌入模型。正在从头重建 Copilot 索引。\")", "Notice(\"Copilot vault index is up-to-date.\")": "Notice(\"Copilot 保管库(Vault)索引是最新的。\")", "Notice();\n      const allChunks = await this.prepareAllChunks(files)": "Notice();\n      const allChunks = await this.prepareAllChunks(files)", "Notice(\"No valid content to index.\")": "Notice(\"没有有效内容可供索引。\")", "Notice() {\n    const frag = document.createDocumentFragment()": "Notice() {\n    const frag = document.createDocumentFragment()", "Notice(frag, 0)": "Notice(frag, 0)", "Notice(\"No files to index with current filters\")": "Notice(\"当前筛选条件下没有可索引的文件\")", "Notice(`Indexing cancelled`)": "Notice(`索引已取消`)", "Notice(`Indexing completed with ${errors2.length} errors. Check console for details.`)": "Notice(`索引完成，出现 ${errors2.length} 个错误。请查看控制台以获取详细信息。`)", "Notice(\"Indexing completed successfully!\")": "Notice(\"索引完成成功！\")", "Notice(\"Fatal error during indexing. Check console for details.\")": "Notice(\"索引过程中发生致命错误。请检查控制台以获取详细信息。\")", "Notice(\"Indexing is disabled on mobile devices\")": "Notice(\"移动设备上已禁用索引\")", "Notice(`Pomodoro timer (${interval})": "Notice(`Pomodoro timer (${interval})", "Notice(errorMsg)": "Notice(errorMsg)", "Notice(\"Please fill in both fields: Title and Prompt.\")": "Notice(\"请同时填写两个字段：标题和聊天提示词\")", "Notice(\"No valid note paths found. Use format: - [[Note Name]]\")": "Notice(\"未找到有效的笔记路径。请使用格式：- [[笔记名称]]\")", "Notice(\"Error searching database. Check console for details.\")": "Notice(\"搜索数据库时出错。请检查控制台以获取详细信息。\")", "Notice(\"No active note found.\")": "Notice(\"未找到激活的文档\")", "Notice(\"Please select a language.\")": "Notice(\"请选择一种语言。\")", "Notice(\"Please select a tone.\")": "Notice(\"请选中一种对话风格/语气。\")", "Notice(`Total tokens in your vault: ${totalTokens}`)": "Notice(`您的保管库(Vault)中的Tokens总数:${totalTokens}`)", "Notice(\"Custom prompt saved successfully.\")": "Notice(\"自定义聊天提示词已成功保存。\")", "Notice(\"Error saving custom prompt. Please check if the title already exists.\")": "Notice(\"保存自定义聊天提示词时出错，请检查标题是否已存在。\")", "Notice(\"Please select a prompt title.\")": "Notice(\"请选择一个聊天提示词的标题\")", "Notice(`No prompt found with the title \"${promptTitle}\".`)": "Notice(`未找到标题为\"${promptTitle}\"的聊天提示词。`)", "Notice(\"An error occurred.\")": "Notice(\"发生了一个错误。\")", "Notice(`Prompt \"${promptTitle}\" has been deleted.`)": "Notice(`聊天提示词\"${promptTitle}\"已被删除。`)", "Notice(\"An error occurred while deleting the prompt.\")": "Notice(\"删除聊天提示词时出错。\")", "Notice(`Prompt \"${title}\" has been updated.`)": "Notice(`聊天提示词\"${title}\"已更新。`)", "Notice(err.message)": "Notice(err.message)", "Notice(`${removedDocs} documents removed from Copilot index.`)": "Notice(`从 Copilot 索引中移除了 ${removedDocs} 个文档。`)", "Notice(\"An error occurred while garbage collecting the Copilot index.\")": "Notice(\"清理 Copilot 索引时发生错误。\")", "Notice(`${indexedFileCount} vault files indexed to Copilot index.`)": "Notice(`${indexedFileCount} 个库文件已索引到 Copilot 索引中。`)", "Notice(\"An error occurred while indexing vault to Copilot index.\")": "Notice(\"在将保险库索引到Copilot索引时发生错误。\")", "Notice(`${indexedFileCount} vault files re-indexed to Copilot index.`)": "Notice(`${indexedFileCount} 个库文件已重新索引到 Copilot 索引。`)", "Notice(\"An error occurred while re-indexing vault to Copilot index.\")": "Notice(\"重新索引保险库到Copilot索引时发生错误。\")", "Notice(\"No files found to list.\")": "Notice(\"未找到可列出的文件。\")", "Notice(`Listed ${indexedFiles.length} indexed files`)": "Notice(`已列出 ${indexedFiles.length} 个索引文件`)", "Notice(\"Failed to list indexed files.\")": "Notice(\"无法列出索引文件。\")", "Notice(`Successfully removed ${filePaths.length} files from the index.`)": "Notice(`成功从索引中移除了 ${filePaths.length} 个文件。`)", "Notice(\"An error occurred while removing files from the index.\")": "Notice(\"从索引中移除文件时发生错误。\")", "Notice(\"Vault index refreshed.\")": "Notice(\"保管库(Vault)中的索引已刷新。\")", "Notice(\"Failed to refresh vault index. Check console for details.\")": "Notice(\"刷新保管库(Vault)的索引失败。查看控制台了解详细信息。\")", "Notice(`Reindexed ${activeFile.name}`)": "Notice(`Reindexed ${activeFile.name}`)", "Notice(\"No messages to save.\")": "Notice(\"没有要保存的消息。\")", "Notice(`Chat updated in existing note: ${noteFileName}`)": "Notice(`聊天记录已在现有文档中更新:${noteFileName}`)", "Notice(`Chat saved as new note: ${noteFileName}`)": "Notice(`聊天另存为新笔记: ${noteFileName}`)", "Notice(\"Failed to save chat as note. Check console for details.\")": "Notice(\"无法将聊天另存为文档，请查看控制台了解详细信息。\")", "Notice(\"Cannot regenerate the first message or a user message.\")": "Notice(\"无法重新生成第一条消息或用户消息。\")", "Notice(\"Failed to regenerate message. Please try again.\")": "Notice(\"重新生成消息失败，请再试一次。\")", "Notice(\"Plugin reloaded successfully.\")": "Notice(\"插件已成功重新加载。\")", "Notice(\"Failed to reload the plugin. Please reload manually.\")": "Notice(\"无法重新加载插件，请手动重新加载。\")", "Notice(\"No chat history found.\")": "Notice(\"未找到聊天记录。\")", "Notice(\n          `A newer version (${latestVersion})": "Notice(\n          `A newer version (${latestVersion})", ".log(\"Using RetryOperation.try()": ".log(\"Using RetryOperation.try()", ".log(\"Using RetryOperation.start()": ".log(\"Using RetryOperation.start()", ".log(`Dataset ${finalDatasetName} already exists in your tenant. Skipping.`)": ".log(`数据集 ${finalDatasetName} 已存在于您的租户中。跳过。`)", ".log(\"New LLM chain created.\")": ".log(\"创建了新的LLM链。\")", ".log(\"Failed to parse error response as JSON\")": ".log(\"未能将错误响应解析为JSON\")", ".log(\"Getting text from response\")": ".log(\"从响应中获取文本\")", ".log(\"Failed to get text from error response\")": ".log(\"无法从错误响应中获取文本\")", ".log(\"[WARNING]: Received non-string content from OpenAI. This is currently not supported.\")": ".log(\"[警告]: 从OpenAI接收到非字符串内容。目前不支持此类型内容。\")", ".log(\"Failed to reinitialize model due to missing API key\")": ".log(\"由于缺少API密钥，无法重新初始化模型\")", ".log(\"First ping attempt failed, trying with CORS...\")": ".log(\"首次 ping 尝试失败，正在尝试使用 CORS...\")", ".log(`Total documents to distribute: ${documents.length}`)": ".log(`待分发的文档总数：${documents.length}`)", ".log(`Partition ${i3 + 1}: ${docs.length} documents`)": ".log(`Partition ${i3 + 1}: ${docs.length} documents`)", ".log(`Total documents distributed: ${totalDistributed}`)": ".log(`已分发的文档总数：${totalDistributed}`)", ".log(`Starting save with ${rawDocs.length ?? 0} total documents`)": ".log(`开始保存，共 ${rawDocs.length ?? 0} 个文档`)", ".log(\"Saved empty database state\")": ".log(\"已保存空数据库状态\")", ".log(`Saved partition ${partitionIndex + 1}/${numPartitions}`)": ".log(`已保存分区 ${partitionIndex + 1}/${numPartitions}`)", ".log(\"Saved all partitions\")": ".log(\"已保存所有分区\")", ".log(\"Path change detected, reinitializing database...\")": ".log(\"检测到路径更改，正在重新初始化数据库...\")", ".log(\"Database reinitialized with new path:\", newPath)": ".log(\"数据库已使用新路径重新初始化：\", newPath)", ".log(\"Loaded existing chunked Orama database from disk.\")": ".log(\"已从磁盘加载现有的分块 Orama 数据库。\")", ".log(\"Failed to load existing database, creating new one:\", error)": ".log(\"无法加载现有数据库，正在创建新数据库：\", error)", ".log(\"Orama database saved successfully at:\", this.dbPath)": ".log(\"Orama 数据库已成功保存至：\", this.dbPath)", ".log(\"Local Copilot index cleared successfully, new instance created.\")": ".log(\"本地 Copilot 索引已成功清除，新实例已创建。\")", ".log(`Deleted document from local Copilot index: ${filePath}`)": ".log(`已从本地 Copilot 索引中删除文档：${filePath}`)", ".log(\"Created directory:\", baseDir)": ".log(\"已创建目录：\", baseDir)", ".log(\"Detected change in embedding model. Rebuilding Copilot index from scratch.\")": ".log(\"检测到嵌入模型发生变化。正在从头重建 Copilot 索引。\")", ".log(\"No previous embedding model found in the database.\")": ".log(\"在数据库中找不到以前的嵌入模型。\")", ".log(\n        \"Copilot index: Docs to remove during garbage collection:\",\n        Array.from(new Set(docsToRemove.map((doc)": ".log(\n        \"Copilot 索引：垃圾回收期间要删除的文档：\",\n        Array.from(new Set(docsToRemove.map((doc)", ".log(\"Copilot Plus: Triggering reindex for file \", file.path)": ".log(\"Copilot Plus：触发文件重新索引\", file.path)", ".log(\"Copilot Plus: Initializing event listeners\")": ".log(\"Copilot Plus：初始化事件监听器\")", ".log(\"Copilot index checkpoint save completed.\")": ".log(\"Copilot 索引检查点保存完成。\")", ".log(\"Copilot index final save completed.\")": ".log(\"Copilot 索引最终保存完成。\")", ".log(`Files to index: ${filesToIndex.size}`)": ".log(`要索引的文件：${filesToIndex.size}`)", ".log(`Previously indexed: ${indexedFilePaths.size}`)": ".log(`之前已索引：${indexedFilePaths.size}`)", ".log(`Empty files skipped: ${emptyFiles.size}`)": ".log(`跳过的空文件：${emptyFiles.size}`)", ".log(\"No files to index after filter change, stopping indexing\")": ".log(\"筛选条件更改后没有文件可索引，停止索引\")", ".log(\"Total files to index:\", this.state.totalFilesToIndex)": ".log(\"待索引文件总数：\", this.state.totalFilesToIndex)", ".log(\"Files to index:\", files)": ".log(\"要索引的文件：\", files)", ".log(`Reindexed file: ${file.path}`)": ".log(`已重新索引文件：${file.path}`)", ".log(\"Indexing cancelled by user\")": ".log(\"索引已由用户取消\")", ".log(\"*** HYBRID RETRIEVER DEBUG INFO: ***\")": ".log(\"*** 混合检索器调试信息： ***\")", ".log(\"Memory initialized with context turns:\", chatContextTurns)": ".log(\"记忆已初始化为上下文轮次\", chatContextTurns)", ".log(\"Clearing chat memory\")": ".log(\"清除聊天记录\")", ".log(\"No local search results. Using standard LLM Chain.\")": ".log(\"无本地搜索结果。使用标准LLM链。\")", ".log(\"Enhanced user message:\", enhancedUserMessage)": ".log(\"好的，请提供需要翻译的英文文本，我将根据插件名称和软件翻译标准，将其翻译为符合中文习惯的简体中文。\", enhancedUserMessage)", ".log(`==== Step 2: Calling tool: ${toolCall.tool.name} ====`)": ".log(`==== Step 2: Calling tool: ${toolCall.tool.name} ====`)", ".log(`Setting model to ${newModelKey}`)": ".log(`Setting model to ${newModelKey}`)", ".log(\"modelKey:\", newModelKey)": ".log(\"modelKey:\", newModelKey)", ".log(\"New Vault QA chain with hybrid retriever created for entire vault\")": ".log(\"为整个保管库(Vault)创建了带有混合检索器的新QA链\")", ".log(\"Set chain:\", \"vault_qa\" /* VAULT_QA_CHAIN */)": ".log(\"Set chain:\", \"vault_qa\" /* VAULT_QA_CHAIN */)", ".log(\"==== Step 0: Initial user message ====\\n\", userMessage)": ".log(\"==== Step 0: Initial user message ====\\n\", userMessage)", ".log(\"No hits found for note:\", notePath)": ".log(\"No hits found for note:\", notePath)", ".log(\"No embedding found for note:\", notePath)": ".log(\"No embedding found for note:\", notePath)", ".log(\"No embeddings found for note:\", filePath)": ".log(\"No embeddings found for note:\", filePath)", ".log(`stopping generation..., reason: ${reason}`)": ".log(`stopping generation..., reason: ${reason}`)", ".log(\"Message regenerated successfully\")": ".log(\"消息已成功重新生成\")", ".log(\"Copilot plugin unloaded\")": ".log(\"Copilot plugin unloaded\")", " error() {\n            if (this._error)": " error() {\n            if (this._error)", " error() {\n                  return new ZodError(newCtx.common.issues)": " error() {\n                  return new ZodError(newCtx.common.issues)", " error() {\n                return new ZodError(newCtx.common.issues)": " error() {\n                return new ZodError(newCtx.common.issues)", ".error(\"SEMVER\", ...args)": ".error(\"SEMVER\", ...args)", "_error();\n    init_fetch()": "_error();\n    init_fetch()", ".error(`An error occurred while creating dataset ${finalDatasetName}. You should delete it manually.`)": ".error(`An error occurred while creating dataset ${finalDatasetName}. You should delete it manually.`)", ".error(`Error in postRun for run ${this.id}:`, error)": ".error(`Error in postRun for run ${this.id}:`, error)", ".error(`Error in patchRun for run ${this.id}`, error)": ".error(`Error in patchRun for run ${this.id}`, error)", " error(format)": " error(format)", " error(\"Can't call %s on a component that is not yet mounted. This is a no-op, but it might indicate a bug in your application. Instead, assign to `this.state` directly or define a `state = {};` class property with the desired state in the %s component.\", callerName, componentName)": " error(\"无法在尚未挂载的组件上调用 %s。这是一个无操作，但可能表明您的应用程序中存在错误。请直接赋值给 `this.state` 或在 %s 组件中定义 `state = {};` 类属性以设置所需状态。\", callerName, componentName)", " error(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\", typeName(value)": " error(\"提供的键是%s类型，不支持。在使用之前，必须将此值强制转换为字符串。\", typeName(value)", " error(\"Received an unexpected object in getComponentNameFromType()": " error(\"Received an unexpected object in getComponentNameFromType()", " error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)": " error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", " error(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)": " error(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", " error('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef()": " error('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef()", " error(\"Rendering <Context.Consumer.Provider> is not supported and will be removed in a future major release. Did you mean to render <Context.Provider> instead?\")": " error(\"Rendering <Context.Consumer.Provider> is not supported and will be removed in a future major release. Did you mean to render <Context.Provider> instead?\")", " error(\"Rendering <Context.Consumer.Consumer> is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?\")": " error(\"Rendering <Context.Consumer.Consumer> is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?\")", " error(\"lazy: Expected the result of a dynamic import()": " error(\"lazy: Expected the result of a dynamic import()", " error(\"React.lazy(...)": " error(\"React.lazy(...)", " error(\"forwardRef requires a render function but received a `memo` component. Instead of forwardRef(memo(...)": " error(\"forwardRef requires a render function but received a `memo` component. Instead of forwardRef(memo(...)", " error(\"forwardRef requires a render function but was given %s.\", render3 === null ? \"null\" : typeof render3)": " error(\"forwardRef requires a render function but was given %s.\", render3 === null ? \"null\" : typeof render3)", " error(\"forwardRef render functions accept exactly two parameters: props and ref. %s\", render3.length === 1 ? \"Did you forget to use the ref parameter?\" : \"Any additional parameter will be undefined.\")": " error(\"forwardRef render functions accept exactly two parameters: props and ref. %s\", render3.length === 1 ? \"Did you forget to use the ref parameter?\" : \"Any additional parameter will be undefined.\")", " error(\"forwardRef render functions do not support propTypes or defaultProps. Did you accidentally pass a React component?\")": " error(\"forwardRef render functions do not support propTypes or defaultProps. Did you accidentally pass a React component?\")", " error(\"memo: The first argument must be a component. Instead received: %s\", type === null ? \"null\" : typeof type)": " error(\"memo: The first argument must be a component. Instead received: %s\", type === null ? \"null\" : typeof type)", " error(\"Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\\n1. You might have mismatching versions of React and the renderer (such as React DOM)": " error(\"Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\\n1. You might have mismatching versions of React and the renderer (such as React DOM)", " error(\"Calling useContext(Context.Consumer)": " error(\"Calling useContext(Context.Consumer)", " error(\"Calling useContext(Context.Provider)": " error(\"Calling useContext(Context.Provider)", " error(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")": " error(\"disabled<PERSON><PERSON><PERSON> fell below zero. This is a bug in React. Please file an issue.\")", " error(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)": " error(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)", " error(\"Failed %s type: %s\", location2, error$1.message)": " error(\"Failed %s type: %s\", location2, error$1.message)", " error('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner)": " error('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner)", " error(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\", _name || \"Unknown\")": " error(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\", _name || \"Unknown\")", " error(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")": " error(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")", " error(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\", key)": " error(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\", key)", " error(\"Invalid attribute `ref` supplied to `React.Fragment`.\")": " error(\"Invalid attribute `ref` supplied to `React.Fragment`.\")", " error(\"React.createElement: type is invalid -- expected a string (for built-in components)": " error(\"React.createElement: type is invalid -- expected a string (for built-in components)", " error(\"This browser does not have a MessageChannel implementation, so enqueuing tasks via await act(async ()": " error(\"This browser does not have a MessageChannel implementation, so enqueuing tasks via await act(async ()", " error(\"You called act(async ()": " error(\"You called act(async ()", " error(\"You seem to have overlapping act()": " error(\"You seem to have overlapping act()", ".error(\n        \"This browser lacks typed array (Uint8Array)": ".error(\n        \"This browser lacks typed array (Uint8Array)", ".error(`Unable to parse ${JSON.stringify(value)": ".error(`Unable to parse ${JSON.stringify(value)", ".error(`Failed to construct URL with ${expression}`, error)": ".error(`Failed to construct URL with ${expression}`, error)", " error() {\n      }\n    };\n  }\n})": " error() {\n      }\n    };\n  }\n})", "nerror(er)": "nerror(er)", "_error(error)": "_error(error)", ".error(`Could not parse message into JSON:`, sse.data)": ".error(`Could not parse message into JSON:`, sse.data)", ".error(`From chunk:`, sse.raw)": ".error(`From chunk:`, sse.raw)", ".error(err)": ".error(err)", " error(\"EventRegistry: More than one plugin attempted to publish the same registration name, `%s`.\", registrationName)": " error(\"EventRegistry: More than one plugin attempted to publish the same registration name, `%s`.\", registrationName)", " error(\"The provided `%s` attribute is an unsupported type %s. This value must be coerced to a string before before using it here.\", attributeName, typeName(value)": " error(\"The provided `%s` attribute is an unsupported type %s. This value must be coerced to a string before before using it here.\", attributeName, typeName(value)", " error(\"The provided `%s` prop is an unsupported type %s. This value must be coerced to a string before before using it here.\", propName, typeName(value)": " error(\"The provided `%s` prop is an unsupported type %s. This value must be coerced to a string before before using it here.\", propName, typeName(value)", " error(\"The provided `%s` CSS property is an unsupported type %s. This value must be coerced to a string before before using it here.\", propName, typeName(value)": " error(\"The provided `%s` CSS property is an unsupported type %s. This value must be coerced to a string before before using it here.\", propName, typeName(value)", " error(\"The provided HTML markup uses a value of unsupported type %s. This value must be coerced to a string before before using it here.\", typeName(value)": " error(\"The provided HTML markup uses a value of unsupported type %s. This value must be coerced to a string before before using it here.\", typeName(value)", " error(\"Form field values (value, checked, defaultValue, or defaultChecked props)": " error(\"Form field values (value, checked, defaultValue, or defaultChecked props)", " error(\"Invalid attribute name: `%s`\", attributeName)": " error(\"Invalid attribute name: `%s`\", attributeName)", " error(\"A future version of React will block javascript: URLs as a security precaution. Use event handlers instead if you can. If you need to generate unsafe HTML try using dangerouslySetInnerHTML instead. React was passed %s.\", JSON.stringify(url)": " error(\"A future version of React will block javascript: URLs as a security precaution. Use event handlers instead if you can. If you need to generate unsafe HTML try using dangerouslySetInnerHTML instead. React was passed %s.\", JSON.stringify(url)", " error(\"You provided a `value` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultValue`. Otherwise, set either `onChange` or `readOnly`.\")": " error(\"You provided a `value` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultValue`. Otherwise, set either `onChange` or `readOnly`.\")", " error(\"You provided a `checked` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultChecked`. Otherwise, set either `onChange` or `readOnly`.\")": " error(\"You provided a `checked` prop to a form field without an `onChange` handler. This will render a read-only field. If the field should be mutable use `defaultChecked`. Otherwise, set either `onChange` or `readOnly`.\")", " error(\"%s contains an input of type %s with both checked and defaultChecked props. Input elements must be either controlled or uncontrolled (specify either the checked prop, or the defaultChecked prop, but not both)": " error(\"%s contains an input of type %s with both checked and defaultChecked props. Input elements must be either controlled or uncontrolled (specify either the checked prop, or the defaultChecked prop, but not both)", " error(\"%s contains an input of type %s with both value and defaultValue props. Input elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both)": " error(\"%s contains an input of type %s with both value and defaultValue props. Input elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both)", " error(\"Cannot infer the option value of complex children. Pass a `value` prop or use a plain string as children to <option>.\")": " error(\"Cannot infer the option value of complex children. Pass a `value` prop or use a plain string as children to <option>.\")", " error(\"Pass a `value` prop if you set dangerouslyInnerHTML so React knows which value should be selected.\")": " error(\"Pass a `value` prop if you set dangerouslyInnerHTML so React knows which value should be selected.\")", " error(\"Use the `defaultValue` or `value` props on <select> instead of setting `selected` on <option>.\")": " error(\"Use the `defaultValue` or `value` props on <select> instead of setting `selected` on <option>.\")", " error(\"The `%s` prop supplied to <select> must be an array if `multiple` is true.%s\", propName, getDeclarationErrorAddendum()": " error(\"The `%s` prop supplied to <select> must be an array if `multiple` is true.%s\", propName, getDeclarationErrorAddendum()", " error(\"The `%s` prop supplied to <select> must be a scalar value if `multiple` is false.%s\", propName, getDeclarationErrorAddendum()": " error(\"The `%s` prop supplied to <select> must be a scalar value if `multiple` is false.%s\", propName, getDeclarationErrorAddendum()", " error(\"Select elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both)": " error(\"Select elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both)", " error(\"%s contains a textarea with both value and defaultValue props. Textarea elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both)": " error(\"%s contains a textarea with both value and defaultValue props. Textarea elements must be either controlled or uncontrolled (specify either the value prop, or the defaultValue prop, but not both)", " error(\"Use the `defaultValue` or `value` props instead of setting children on <textarea>.\")": " error(\"Use the `defaultValue` or `value` props instead of setting children on <textarea>.\")", " error(\n              \"Unsupported style property %s. Did you mean %s?\",\n              name,\n              // As Andi Smith suggests\n              // (http://www.andismith.com/blog/2012/02/modernizr-prefixed/)": " error(\n              \"Unsupported style property %s. Did you mean %s?\",\n              name,\n              // As <PERSON><PERSON> suggests\n              // (http://www.andismith.com/blog/2012/02/modernizr-prefixed/)", " error(\"Unsupported vendor-prefixed style property %s. Did you mean %s?\", name, name.charAt(0)": " error(\"Unsupported vendor-prefixed style property %s. Did you mean %s?\", name, name.charAt(0)", " error(`Style property values shouldn't contain a semicolon. Try \"%s: %s\" instead.`, name, value.replace(badStyleValueWithSemicolonPattern, \"\")": " error(`Style property values shouldn't contain a semicolon. Try \"%s: %s\" instead.`, name, value.replace(badStyleValueWithSemicolonPattern, \"\")", " error(\"`NaN` is an invalid value for the `%s` css style property.\", name)": " error(\"`NaN` is an invalid value for the `%s` css style property.\", name)", " error(\"`Infinity` is an invalid value for the `%s` css style property.\", name)": " error(\"`Infinity` is an invalid value for the `%s` css style property.\", name)", " error(\"%s a style property during rerender (%s)": " error(\"%s a style property during rerender (%s)", " error(\"A component is `contentEditable` and contains `children` managed by React. It is now your responsibility to guarantee that none of those nodes are unexpectedly modified or duplicated. This is probably not intentional.\")": " error(\"A component is `contentEditable` and contains `children` managed by React. It is now your responsibility to guarantee that none of those nodes are unexpectedly modified or duplicated. This is probably not intentional.\")", " error(\"Invalid ARIA attribute `%s`. ARIA attributes follow the pattern aria-* and must be lowercase.\", name)": " error(\"Invalid ARIA attribute `%s`. ARIA attributes follow the pattern aria-* and must be lowercase.\", name)", " error(\"Invalid ARIA attribute `%s`. Did you mean `%s`?\", name, correctName)": " error(\"Invalid ARIA attribute `%s`. Did you mean `%s`?\", name, correctName)", " error(\"Unknown ARIA attribute `%s`. Did you mean `%s`?\", name, standardName)": " error(\"Unknown ARIA attribute `%s`. Did you mean `%s`?\", name, standardName)", " error(\"Invalid aria prop %s on <%s> tag. For details, see https://reactjs.org/link/invalid-aria-props\", unknownPropString, type)": " error(\"Invalid aria prop %s on <%s> tag. For details, see https://reactjs.org/link/invalid-aria-props\", unknownPropString, type)", " error(\"Invalid aria props %s on <%s> tag. For details, see https://reactjs.org/link/invalid-aria-props\", unknownPropString, type)": " error(\"Invalid aria props %s on <%s> tag. For details, see https://reactjs.org/link/invalid-aria-props\", unknownPropString, type)", " error(\"`value` prop on `%s` should not be null. Consider using an empty array when `multiple` is set to `true` to clear the component or `undefined` for uncontrolled components.\", type)": " error(\"`value` prop on `%s` should not be null. Consider using an empty array when `multiple` is set to `true` to clear the component or `undefined` for uncontrolled components.\", type)", " error(\"`value` prop on `%s` should not be null. Consider using an empty string to clear the component or `undefined` for uncontrolled components.\", type)": " error(\"`value` prop on `%s` should not be null. Consider using an empty string to clear the component or `undefined` for uncontrolled components.\", type)", " error(\"React uses onFocus and onBlur instead of onFocusIn and onFocusOut. All React events are normalized to bubble, so onFocusIn and onFocusOut are not needed/supported by React.\")": " error(\"React uses onFocus and onBlur instead of onFocusIn and onFocusOut. All React events are normalized to bubble, so onFocusIn and onFocusOut are not needed/supported by React.\")", " error(\"Invalid event handler property `%s`. Did you mean `%s`?\", name, registrationName)": " error(\"Invalid event handler property `%s`. Did you mean `%s`?\", name, registrationName)", " error(\"Unknown event handler property `%s`. It will be ignored.\", name)": " error(\"Unknown event handler property `%s`. It will be ignored.\", name)", " error(\"Invalid event handler property `%s`. React events use the camelCase naming convention, for example `onClick`.\", name)": " error(\"Invalid event handler property `%s`. React events use the camelCase naming convention, for example `onClick`.\", name)", " error(\"Directly setting property `innerHTML` is not permitted. For more information, lookup documentation on `dangerouslySetInnerHTML`.\")": " error(\"Directly setting property `innerHTML` is not permitted. For more information, lookup documentation on `dangerouslySetInnerHTML`.\")", " error(\"The `aria` attribute is reserved for future use in React. Pass individual `aria-` attributes instead.\")": " error(\"The `aria` attribute is reserved for future use in React. Pass individual `aria-` attributes instead.\")", " error(\"Received a `%s` for a string attribute `is`. If this is expected, cast the value to a string.\", typeof value)": " error(\"Received a `%s` for a string attribute `is`. If this is expected, cast the value to a string.\", typeof value)", " error(\"Received NaN for the `%s` attribute. If this is expected, cast the value to a string.\", name)": " error(\"Received NaN for the `%s` attribute. If this is expected, cast the value to a string.\", name)", " error(\"Invalid DOM property `%s`. Did you mean `%s`?\", name, standardName)": " error(\"Invalid DOM property `%s`. Did you mean `%s`?\", name, standardName)", " error(\"React does not recognize the `%s` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `%s` instead. If you accidentally passed it from a parent component, remove it from the DOM element.\", name, lowerCasedName)": " error(\"React does not recognize the `%s` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `%s` instead. If you accidentally passed it from a parent component, remove it from the DOM element.\", name, lowerCasedName)", " error('Received `%s` for a non-boolean attribute `%s`.\\n\\nIf you want to write it to the DOM, pass a string instead: %s=\"%s\" or %s={value.toString()": " error('Received `%s` for a non-boolean attribute `%s`.\\n\\nIf you want to write it to the DOM, pass a string instead: %s=\"%s\" or %s={value.toString()", " error(\"Received the string `%s` for the boolean attribute `%s`. %s Did you mean %s={%s}?\", value, name, value === \"false\" ? \"The browser will interpret it as a truthy value.\" : 'Although this works, it will not work as expected if you pass the string \"false\".', name, value)": " error(\"Received the string `%s` for the boolean attribute `%s`. %s Did you mean %s={%s}?\", value, name, value === \"false\" ? \"The browser will interpret it as a truthy value.\" : 'Although this works, it will not work as expected if you pass the string \"false\".', name, value)", " error(\"Invalid value for prop %s on <%s> tag. Either remove it from the element, or pass a string or number value to keep it in the DOM. For details, see https://reactjs.org/link/attribute-behavior \", unknownPropString, type)": " error(\"Invalid value for prop %s on <%s> tag. Either remove it from the element, or pass a string or number value to keep it in the DOM. For details, see https://reactjs.org/link/attribute-behavior \", unknownPropString, type)", " error(\"Invalid values for props %s on <%s> tag. Either remove them from the element, or pass a string or number value to keep them in the DOM. For details, see https://reactjs.org/link/attribute-behavior \", unknownPropString, type)": " error(\"Invalid values for props %s on <%s> tag. Either remove them from the element, or pass a string or number value to keep them in the DOM. For details, see https://reactjs.org/link/attribute-behavior \", unknownPropString, type)", " error(\"Expected currently replaying event to be null. This error is likely caused by a bug in React. Please file an issue.\")": " error(\"Expected currently replaying event to be null. This error is likely caused by a bug in React. Please file an issue.\")", " error(\"Expected currently replaying event to not be null. This error is likely caused by a bug in React. Please file an issue.\")": " error(\"Expected currently replaying event to not be null. This error is likely caused by a bug in React. Please file an issue.\")", " error(\"%s is accessing isMounted inside its render()": " error(\"%s is accessing isMounted inside its render()", " error(\"The installed version of React DevTools is too old and will not work with the current version of React. Please update React DevTools. https://reactjs.org/link/react-devtools\")": " error(\"The installed version of React DevTools is too old and will not work with the current version of React. Please update React DevTools. https://reactjs.org/link/react-devtools\")", " error(\"React instrumentation encountered an error: %s.\", err)": " error(\"React instrumentation encountered an error: %s.\", err)", " error(\"React instrumentation encountered an error: %s\", err)": " error(\"React instrumentation encountered an error: %s\", err)", " error(\"Should have found matching lanes. This is a bug in React.\")": " error(\"Should have found matching lanes. This is a bug in React.\")", " error('Did not expect a listenToNonDelegatedEvent()": " error('Did not expect a listenToNonDelegatedEvent()", " error('Did not expect a listenToNativeEvent()": " error('Did not expect a listenToNativeEvent()", " error(\"Prop `%s` did not match. Server: %s Client: %s\", propName, JSON.stringify(normalizedServerValue)": " error(\"Prop `%s` did not match. Server: %s Client: %s\", propName, JSON.stringify(normalizedServerValue)", " error(\"Extra attributes from the server: %s\", names)": " error(\"Extra attributes from the server: %s\", names)", " error(\"Expected `%s` listener to be a function, instead got `false`.\\n\\nIf you used to conditionally omit it with %s={condition && value}, pass %s={condition ? value : undefined} instead.\", registrationName, registrationName, registrationName)": " error(\"Expected `%s` listener to be a function, instead got `false`.\\n\\nIf you used to conditionally omit it with %s={condition && value}, pass %s={condition ? value : undefined} instead.\", registrationName, registrationName, registrationName)", " error(\"Expected `%s` listener to be a function, instead got a value of `%s` type.\", registrationName, typeof listener)": " error(\"Expected `%s` listener to be a function, instead got a value of `%s` type.\", registrationName, typeof listener)", " error('Text content did not match. Server: \"%s\" Client: \"%s\"', normalizedServerText, normalizedClientText)": " error('Text content did not match. Server: \"%s\" Client: \"%s\"', normalizedServerText, normalizedClientText)", " error(\"<%s /> is using incorrect casing. Use PascalCase for React components, or lowercase for HTML elements.\", type)": " error(\"<%s /> is using incorrect casing. Use PascalCase for React components, or lowercase for HTML elements.\", type)", " error(\"The tag <%s> is unrecognized in this browser. If you meant to render a React component, start its name with an uppercase letter.\", type)": " error(\"The tag <%s> is unrecognized in this browser. If you meant to render a React component, start its name with an uppercase letter.\", type)", " error(\"Did not expect server HTML to contain a <%s> in <%s>.\", child.nodeName.toLowerCase()": " error(\"Did not expect server HTML to contain a <%s> in <%s>.\", child.nodeName.toLowerCase()", " error('Did not expect server HTML to contain the text node \"%s\" in <%s>.', child.nodeValue, parentNode.nodeName.toLowerCase()": " error('Did not expect server HTML to contain the text node \"%s\" in <%s>.', child.nodeValue, parentNode.nodeName.toLowerCase()", " error(\"Expected server HTML to contain a matching <%s> in <%s>.\", tag, parentNode.nodeName.toLowerCase()": " error(\"Expected server HTML to contain a matching <%s> in <%s>.\", tag, parentNode.nodeName.toLowerCase()", " error('Expected server HTML to contain a matching text node for \"%s\" in <%s>.', text, parentNode.nodeName.toLowerCase()": " error('Expected server HTML to contain a matching text node for \"%s\" in <%s>.', text, parentNode.nodeName.toLowerCase()", " error(\"validateDOMNesting: when childText is passed, childTag should be null\")": " error(\"validateDOMNesting: when childText is passed, childTag should be null\")", " error(\"validateDOMNesting(...)": " error(\"validateDOMNesting(...)", " error(\"An error occurred during hydration. The server HTML was replaced with client content in <%s>.\", parentContainer.nodeName.toLowerCase()": " error(\"An error occurred during hydration. The server HTML was replaced with client content in <%s>.\", parentContainer.nodeName.toLowerCase()", " error(\"Unexpected pop.\")": " error(\"Unexpected pop.\")", " error(\"Unexpected Fiber popped.\")": " error(\"Unexpected Fiber popped.\")", " error(\"%s.childContextTypes is specified but there is no getChildContext()": " error(\"%s.childContextTypes is specified but there is no getChildContext()", " error(\"Expected to be hydrating. This is a bug in React. Please file an issue.\")": " error(\"Expected to be hydrating. This is a bug in React. Please file an issue.\")", " error(\"We should not be hydrating here. This is a bug in React. Please file a bug.\")": " error(\"We should not be hydrating here. This is a bug in React. Please file a bug.\")", " error(\"Expected to find a StrictMode component in a strict mode tree. This error is likely caused by a bug in React. Please file an issue.\")": " error(\"Expected to find a StrictMode component in a strict mode tree. This error is likely caused by a bug in React. Please file an issue.\")", " error(\"Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported.\")": " error(\"Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported.\")", " error(\"Expected to find the propagation root when scheduling context work. This error is likely caused by a bug in React. Please file an issue.\")": " error(\"Expected to find the propagation root when scheduling context work. This error is likely caused by a bug in React. Please file an issue.\")", " error(\"Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer()": " error(\"Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer()", " error(\"An update (setState, replaceState, or forceUpdate)": " error(\"An update (setState, replaceState, or forceUpdate)", " error(\"%s(...)": " error(\"%s(...)", " error(\"%s.getDerivedStateFromProps()": " error(\"%s.getDerivedStateFromProps()", " error(\"%s.shouldComponentUpdate()": " error(\"%s.shouldComponentUpdate()", " error(\"getInitialState was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Did you mean to define a state property instead?\", name)": " error(\"getInitialState was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Did you mean to define a state property instead?\", name)", " error(\"getDefaultProps was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Use a static property to define defaultProps instead.\", name)": " error(\"getDefaultProps was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Use a static property to define defaultProps instead.\", name)", " error(\"propTypes was defined as an instance property on %s. Use a static property to define propTypes instead.\", name)": " error(\"propTypes was defined as an instance property on %s. Use a static property to define propTypes instead.\", name)", " error(\"contextType was defined as an instance property on %s. Use a static property to define contextType instead.\", name)": " error(\"contextType was defined as an instance property on %s. Use a static property to define contextType instead.\", name)", " error(\"contextTypes was defined as an instance property on %s. Use a static property to define contextTypes instead.\", name)": " error(\"contextTypes was defined as an instance property on %s. Use a static property to define contextTypes instead.\", name)", " error(\"%s declares both contextTypes and contextType static properties. The legacy contextTypes property will be ignored.\", name)": " error(\"%s declares both contextTypes and contextType static properties. The legacy contextTypes property will be ignored.\", name)", " error(\"%s has a method called componentShouldUpdate()": " error(\"%s has a method called componentShouldUpdate()", " error(\"%s has a method called shouldComponentUpdate()": " error(\"%s has a method called shouldComponentUpdate()", " error(\"%s has a method called componentDidUnmount()": " error(\"%s has a method called componentDidUnmount()", " error(\"%s has a method called componentDidReceiveProps()": " error(\"%s has a method called componentDidReceiveProps()", " error(\"%s has a method called componentWillRecieveProps()": " error(\"%s has a method called componentWillRecieveProps()", " error(\"%s has a method called UNSAFE_componentWillRecieveProps()": " error(\"%s has a method called UNSAFE_componentWillRecieveProps()", " error(\"Setting defaultProps as an instance property on %s is not supported and will be ignored. Instead, define defaultProps as a static property on %s.\", name, name)": " error(\"Setting defaultProps as an instance property on %s is not supported and will be ignored. Instead, define defaultProps as a static property on %s.\", name, name)", " error(\"%s: getSnapshotBeforeUpdate()": " error(\"%s: getSnapshotBeforeUpdate()", " error(\"%s: getDerivedStateFromProps()": " error(\"%s: getDerivedStateFromProps()", " error(\"%s: getDerivedStateFromError()": " error(\"%s: getDerivedStateFromError()", " error(\"%s.state: must be set to an object or null\", name)": " error(\"%s.state: must be set to an object or null\", name)", " error(\"%s.getChildContext()": " error(\"%s.get<PERSON><PERSON>d<PERSON>()", " error(\"%s defines an invalid contextType. contextType should point to the Context object returned by React.createContext()": " error(\"%s defines an invalid contextType. contextType should point to the Context object returned by React.createContext()", " error(\"%s.componentWillMount()": " error(\"%s.componentWillMount()", " error(\"%s.componentWillReceiveProps()": " error(\"%s.componentWillReceiveProps()", " error(\"%s: It is not recommended to assign props directly to state because updates to props won't be reflected in state. In most cases, it is better to use props directly.\", componentName)": " error(\"%s: It is not recommended to assign props directly to state because updates to props won't be reflected in state. In most cases, it is better to use props directly.\", componentName)", " error('Each child in a list should have a unique \"key\" prop. See https://reactjs.org/link/warning-keys for more information.')": " error('Each child in a list should have a unique \"key\" prop. See https://reactjs.org/link/warning-keys for more information.')", " error('A string ref, \"%s\", has been found within a strict mode tree. String refs are a source of potential bugs and should be avoided. We recommend using useRef()": " error('A string ref, \"%s\", has been found within a strict mode tree. String refs are a source of potential bugs and should be avoided. We recommend using useRef()", " error(\"Functions are not valid as a React child. This may happen if you return a Component instead of <Component /> from render. Or maybe you meant to call this function rather than return it.\")": " error(\"Functions are not valid as a React child. This may happen if you return a Component instead of <Component /> from render. Or maybe you meant to call this function rather than return it.\")", " error(\"Encountered two children with the same key, `%s`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted \\u2014 the behavior is unsupported and could change in a future version.\", key)": " error(\"Encountered two children with the same key, `%s`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted \\u2014 the behavior is unsupported and could change in a future version.\", key)", " error(\"Using Generators as children is unsupported and will likely yield unexpected results because enumerating a generator mutates it. You may convert it to an array with `Array.from()": " error(\"Using Generators as children is unsupported and will likely yield unexpected results because enumerating a generator mutates it. You may convert it to an array with `Array.from()", " error(\"Using Maps as children is not supported. Use an array of keyed ReactElements instead.\")": " error(\"Using Maps as children is not supported. Use an array of keyed ReactElements instead.\")", " error(\"%s received a final argument that is not an array (instead, received `%s`)": " error(\"%s received a final argument that is not an array (instead, received `%s`)", " error(\"%s received a final argument during this render, but not during the previous render. Even though the final argument is optional, its type cannot change between renders.\", currentHookNameInDev)": " error(\"%s received a final argument during this render, but not during the previous render. Even though the final argument is optional, its type cannot change between renders.\", currentHookNameInDev)", " error(\"The final argument passed to %s changed size between renders. The order and size of this array must remain constant.\\n\\nPrevious: %s\\nIncoming: %s\", currentHookNameInDev, \"[\" + prevDeps.join(\", \")": " error(\"The final argument passed to %s changed size between renders. The order and size of this array must remain constant.\\n\\nPrevious: %s\\nIncoming: %s\", currentHookNameInDev, \"[\" + prevDeps.join(\", \")", " error(\"Internal React error: Expected static flag was missing. Please notify the React team.\")": " error(\"Internal React error: Expected static flag was missing. Please notify the React team.\")", " error(\"Internal error: Expected work-in-progress queue to be a clone. This is a bug in React.\")": " error(\"Internal error: Expected work-in-progress queue to be a clone. This is a bug in React.\")", " error(\"The result of getServerSnapshot should be cached to avoid an infinite loop\")": " error(\"The result of getServerSnapshot should be cached to avoid an infinite loop\")", " error(\"The result of getSnapshot should be cached to avoid an infinite loop\")": " error(\"The result of getSnapshot should be cached to avoid an infinite loop\")", " error(\"Expected useImperativeHandle()": " error(\"Expected useImperativeHandle()", " error(\"State updates from the useState()": " error(\"State updates from the useState()", " error(\"Do not call Hooks inside useEffect(...)": " error(\"Do not call <PERSON><PERSON> inside useEffect(...)", " error(\"%s: Error boundaries should implement getDerivedStateFromError()": " error(\"%s: Error boundaries should implement getDerivedStateFromError()", " error(\"It looks like %s is reassigning its own `this.props` while rendering. This is not supported and can lead to confusing bugs.\", getComponentNameFromFiber(workInProgress2)": " error(\"It looks like %s is reassigning its own `this.props` while rendering. This is not supported and can lead to confusing bugs.\", getComponentNameFromFiber(workInProgress2)", " error(\"The <%s /> component appears to have a render method, but doesn't extend React.Component. This is likely to cause errors. Change %s to extend React.Component instead.\", componentName, componentName)": " error(\"The <%s /> component appears to have a render method, but doesn't extend React.Component. This is likely to cause errors. Change %s to extend React.Component instead.\", componentName, componentName)", " error(\"Function components cannot be given refs. Attempts to access this ref will fail. Did you mean to use React.forwardRef()": " error(\"Function components cannot be given refs. Attempts to access this ref will fail. Did you mean to use React.forwardRef()", " error(\"%s: Function components do not support getDerivedStateFromProps.\", _componentName3)": " error(\"%s: Function components do not support getDerivedStateFromProps.\", _componentName3)", " error(\"%s: Function components do not support contextType.\", _componentName4)": " error(\"%s: Function components do not support contextType.\", _componentName4)", " error(\"Cannot hydrate Suspense in legacy mode. Switch from ReactDOM.hydrate(element, container)": " error(\"Cannot hydrate Suspense in legacy mode. Switch from ReactDOM.hydrate(element, container)", " error('\"%s\" is not a valid value for revealOrder on <SuspenseList />. Use lowercase \"%s\" instead.', revealOrder, revealOrder.toLowerCase()": " error('\"%s\" is not a valid value for revealOrder on <SuspenseList />. Use lowercase \"%s\" instead.', revealOrder, revealOrder.toLowerCase()", " error('\"%s\" is not a valid value for revealOrder on <SuspenseList />. React uses the -s suffix in the spelling. Use \"%ss\" instead.', revealOrder, revealOrder.toLowerCase()": " error('\"%s\" is not a valid value for revealOrder on <SuspenseList />. React uses the -s suffix in the spelling. Use \"%ss\" instead.', revealOrder, revealOrder.toLowerCase()", " error('\"%s\" is not a supported revealOrder on <SuspenseList />. Did you mean \"together\", \"forwards\" or \"backwards\"?', revealOrder)": " error('\"%s\" is not a supported revealOrder on <SuspenseList />. Did you mean \"together\", \"forwards\" or \"backwards\"?', revealOrder)", " error('%s is not a supported value for revealOrder on <SuspenseList />. Did you mean \"together\", \"forwards\" or \"backwards\"?', revealOrder)": " error('%s is not a supported value for revealOrder on <SuspenseList />. Did you mean \"together\", \"forwards\" or \"backwards\"?', revealOrder)", " error('\"%s\" is not a supported value for tail on <SuspenseList />. Did you mean \"collapsed\" or \"hidden\"?', tailMode)": " error('\"%s\" is not a supported value for tail on <SuspenseList />. Did you mean \"collapsed\" or \"hidden\"?', tailMode)", " error('<SuspenseList tail=\"%s\" /> is only valid if revealOrder is \"forwards\" or \"backwards\". Did you mean to specify revealOrder=\"forwards\"?', tailMode)": " error('<SuspenseList tail=\"%s\" /> is only valid if revealOrder is \"forwards\" or \"backwards\". Did you mean to specify revealOrder=\"forwards\"?', tailMode)", " error(\"A nested %s was passed to row #%s in <SuspenseList />. Wrap it in an additional SuspenseList to configure its revealOrder: <SuspenseList revealOrder=...> ... <SuspenseList revealOrder=...>{%s}</SuspenseList> ... </SuspenseList>\", type, index3, type)": " error(\"A nested %s was passed to row #%s in <SuspenseList />. Wrap it in an additional SuspenseList to configure its revealOrder: <SuspenseList revealOrder=...> ... <SuspenseList revealOrder=...>{%s}</SuspenseList> ... </SuspenseList>\", type, index3, type)", " error('A single row was passed to a <SuspenseList revealOrder=\"%s\" />. This is not useful since it needs multiple rows. Did you mean to pass multiple children or an array?', revealOrder)": " error('A single row was passed to a <SuspenseList revealOrder=\"%s\" />. This is not useful since it needs multiple rows. Did you mean to pass multiple children or an array?', revealOrder)", " error(\"The `value` prop is required for the `<Context.Provider>`. Did you misspell it or forget to pass it?\")": " error(\"The `value` prop is required for the `<Context.Provider>`. Did you misspell it or forget to pass it?\")", " error(\"Rendering <Context> directly is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?\")": " error(\"Rendering <Context> directly is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?\")", " error(\"A context consumer was rendered with multiple children, or a child that isn't a function. A context consumer expects a single child that is a function. If you did pass a function, make sure there is no trailing or leading whitespace around it.\")": " error(\"A context consumer was rendered with multiple children, or a child that isn't a function. A context consumer expects a single child that is a function. If you did pass a function, make sure there is no trailing or leading whitespace around it.\")", " error(\"Unexpected return value from a callback ref in %s. A callback ref should not return a function.\", getComponentNameFromFiber(current2)": " error(\"Unexpected return value from a callback ref in %s. A callback ref should not return a function.\", getComponentNameFromFiber(current2)", " error(\"Expected %s props to match memoized props before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.\", getComponentNameFromFiber(finishedWork)": " error(\"Expected %s props to match memoized props before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.\", getComponentNameFromFiber(finishedWork)", " error(\"Expected %s state to match memoized state before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.\", getComponentNameFromFiber(finishedWork)": " error(\"Expected %s state to match memoized state before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.\", getComponentNameFromFiber(finishedWork)", " error(\"%s.getSnapshotBeforeUpdate()": " error(\"%s.getSnapshotBeforeUpdate()", " error(\"%s must not return anything besides a function, which is used for clean-up.%s\", hookName, addendum)": " error(\"%s must not return anything besides a function, which is used for clean-up.%s\", hookName, addendum)", " error(\"Expected %s props to match memoized props before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.\", getComponentNameFromFiber(finishedWork)": " error(\"Expected %s props to match memoized props before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.\", getComponentNameFromFiber(finishedWork)", " error(\"Expected %s state to match memoized state before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.\", getComponentNameFromFiber(finishedWork)": " error(\"Expected %s state to match memoized state before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.\", getComponentNameFromFiber(finishedWork)", " error(\"Expected %s props to match memoized props before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.\", getComponentNameFromFiber(finishedWork)": " error(\"Expected %s props to match memoized props before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.\", getComponentNameFromFiber(finishedWork)", " error(\"Expected %s state to match memoized state before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.\", getComponentNameFromFiber(finishedWork)": " error(\"Expected %s state to match memoized state before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.\", getComponentNameFromFiber(finishedWork)", " error(\"Expected %s props to match memoized props before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.\", getComponentNameFromFiber(finishedWork)": " error(\"Expected %s props to match memoized props before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.\", getComponentNameFromFiber(finishedWork)", " error(\"Expected %s state to match memoized state before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.\", getComponentNameFromFiber(finishedWork)": " error(\"Expected %s state to match memoized state before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.\", getComponentNameFromFiber(finishedWork)", " error(\"Unexpected return value from a callback ref in %s. A callback ref should not return a function.\", getComponentNameFromFiber(finishedWork)": " error(\"Unexpected return value from a callback ref in %s. A callback ref should not return a function.\", getComponentNameFromFiber(finishedWork)", " error(\"Unexpected ref object provided for %s. Use either a ref-setter function or React.createRef()": " error(\"Unexpected ref object provided for %s. Use either a ref-setter function or React.createRef()", " error(\"The current testing environment is not configured to support act(...)": " error(\"The current testing environment is not configured to support act(...)", " error(\"useInsertionEffect must not schedule updates.\")": " error(\"useInsertionEffect must not schedule updates.\")", " error(\"Expected scheduled callback to exist. This error is likely caused by a bug in React. Please file an issue.\")": " error(\"Expected scheduled callback to exist. This error is likely caused by a bug in React. Please file an issue.\")", " error(\"root.finishedLanes should not be empty during a commit. This is a bug in React.\")": " error(\"root.finishedLanes should not be empty during a commit. This is a bug in React.\")", " error(\"Internal React error: Attempted to capture a commit phase error inside a detached tree. This indicates a bug in React. Likely causes include deleting the same fiber more than once, committing an already-finished tree, or an inconsistent return pointer.\\n\\nError message:\\n\\n%s\", error$1)": " error(\"Internal React error: Attempted to capture a commit phase error inside a detached tree. This indicates a bug in React. Likely causes include deleting the same fiber more than once, committing an already-finished tree, or an inconsistent return pointer.\\n\\nError message:\\n\\n%s\", error$1)", " error(\"Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.\")": " error(\"Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.\")", " error(\"Can't perform a React state update on a component that hasn't mounted yet. This indicates that you have a side-effect in your render function that asynchronously later calls tries to update the component. Move this work to useEffect instead.\")": " error(\"Can't perform a React state update on a component that hasn't mounted yet. This indicates that you have a side-effect in your render function that asynchronously later calls tries to update the component. Move this work to useEffect instead.\")", " error(\"Cannot update a component (`%s`)": " error(\"Cannot update a component (`%s`)", " error(\"Cannot update during an existing state transition (such as within `render`)": " error(\"Cannot update during an existing state transition (such as within `render`)", " error(\"An update to %s inside a test was not wrapped in act(...)": " error(\"An update to %s inside a test was not wrapped in act(...)", " error(\"A suspended resource finished loading inside a test, but the event was not wrapped in act(...)": " error(\"A suspended resource finished loading inside a test, but the event was not wrapped in act(...)", " error('Profiler must specify an \"id\" of type `string` as a prop. Received the type `%s` instead.', typeof pendingProps.id)": " error('Profiler must specify an \"id\" of type `string` as a prop. Received the type `%s` instead.', typeof pendingProps.id)", " error(\"%s is deprecated in StrictMode. %s was passed an instance of %s which is inside StrictMode. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node\", methodName, methodName, componentName)": " error(\"%s is deprecated in StrictMode. %s was passed an instance of %s which is inside StrictMode. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node\", methodName, methodName, componentName)", " error(\"Render methods should be a pure function of props and state; triggering nested component updates from render is not allowed. If necessary, trigger nested updates in componentDidUpdate.\\n\\nCheck the render method of %s.\", getComponentNameFromFiber(current)": " error(\"Render methods should be a pure function of props and state; triggering nested component updates from render is not allowed. If necessary, trigger nested updates in componentDidUpdate.\\n\\nCheck the render method of %s.\", getComponentNameFromFiber(current)", " error(\"render(...)": " error(\"render(...)", " error(\"You passed a container to the second argument of root.render(...)": " error(\"You passed a container to the second argument of root.render(...)", " error(\"You passed a second argument to root.render(...)": " error(\"You passed a second argument to root.render(...)", " error(\"unmount(...)": " error(\"unmount(...)", " error(\"Attempted to synchronously unmount a root while React was already rendering. React cannot finish unmounting the root until the current render has completed, which may lead to a race condition.\")": " error(\"Attempted to synchronously unmount a root while <PERSON><PERSON> was already rendering. <PERSON><PERSON> cannot finish unmounting the root until the current render has completed, which may lead to a race condition.\")", " error(\"You passed a JSX element to createRoot. You probably meant to call root.render instead. Example usage:\\n\\n  let root = createRoot(domContainer)": " error(\"You passed a JSX element to createRoot. You probably meant to call root.render instead. Example usage:\\n\\n  let root = createRoot(domContainer)", " error(\"Must provide initial children as second argument to hydrateRoot. Example usage: hydrateRoot(domContainer, <App />)": " error(\"Must provide initial children as second argument to hydrateRoot. Example usage: hydrateRoot(domContainer, <App />)", " error(\"createRoot()": " error(\"createRoot()", " error(\"You are calling ReactDOMClient.createRoot()": " error(\"You are calling ReactDOMClient.createRoot()", " error(\"render()": " error(\"render()", " error(\"%s is accessing findDOMNode inside its render()": " error(\"%s is accessing findDOMNode inside its render()", " error(\"ReactDOM.hydrate is no longer supported in React 18. Use hydrateRoot instead. Until you switch to the new API, your app will behave as if it's running React 17. Learn more: https://reactjs.org/link/switch-to-createroot\")": " error(\"ReactDOM.hydrate is no longer supported in React 18. Use hydrateRoot instead. Until you switch to the new API, your app will behave as if it's running React 17. Learn more: https://reactjs.org/link/switch-to-createroot\")", " error(\"You are calling ReactDOM.hydrate()": " error(\"You are calling ReactDOM.hydrate()", " error(\"ReactDOM.render is no longer supported in React 18. Use createRoot instead. Until you switch to the new API, your app will behave as if it's running React 17. Learn more: https://reactjs.org/link/switch-to-createroot\")": " error(\"ReactDOM.render is no longer supported in React 18. Use createRoot instead. Until you switch to the new API, your app will behave as if it's running React 17. Learn more: https://reactjs.org/link/switch-to-createroot\")", " error(\"You are calling ReactDOM.render()": " error(\"You are calling ReactDOM.render()", " error(\"ReactDOM.unstable_renderSubtreeIntoContainer()": " error(\"ReactDOM.unstable_renderSubtreeIntoContainer()", " error(\"You are calling ReactDOM.unmountComponentAtNode()": " error(\"You are calling ReactDOM.unmountComponentAtNode()", " error(\"unmountComponentAtNode()": " error(\"unmountComponentAtNode()", " error(\"React depends on Map and Set built-in types. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills\")": " error(\"React depends on Map and Set built-in types. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills\")", " error('You are importing createRoot from \"react-dom\" which is not supported. You should instead import it from \"react-dom/client\".')": " error('You are importing createRoot from \"react-dom\" which is not supported. You should instead import it from \"react-dom/client\".')", " error('You are importing hydrateRoot from \"react-dom\" which is not supported. You should instead import it from \"react-dom/client\".')": " error('You are importing hydrateRoot from \"react-dom\" which is not supported. You should instead import it from \"react-dom/client\".')", " error(\"flushSync was called from inside a lifecycle method. React cannot flush when React is already rendering. Consider moving this call to a scheduler task or micro task.\")": " error(\"flushSync was called from inside a lifecycle method. <PERSON>act cannot flush when <PERSON><PERSON> is already rendering. Consider moving this call to a scheduler task or micro task.\")", " error(\"React.jsx: type is invalid -- expected a string (for built-in components)": " error(\"React.jsx: type is invalid -- expected a string (for built-in components)", " error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\")": " error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the <PERSON>l transform instead.\")", ".error(\"Decryption failed:\", err)": ".error(\"Decryption failed:\", err)", ".error(\"Error parsing YAML frontmatter:\", error)": ".error(\"Error parsing YAML frontmatter:\", error)", ".error(new GoogleGenerativeAIError(\"Failed to parse stream\")": ".error(new GoogleGenerativeAIError(\"Failed to parse stream\")", ".error(new GoogleGenerativeAIError(`Error parsing JSON response: \"${match2[1]}\"`)": ".error(new GoogleGenerativeAIError(`Error parsing JSON response: \"${match2[1]}\"`)", ".error(e3)": ".error(e3)", ".error(deltaEntry)": ".error(deltaEntry)", ".error(result.reason)": ".error(result.reason)", ".error(\"Error parsing function arguments\", error, JSON.stringify(openAIMessage.additional_kwargs.function_call)": ".error(\"Error parsing function arguments\", error, JSON.stringify(openAIMessage.additional_kwargs.function_call)", ".error(error)": ".error(error)", ".error(\"Caught error in getAllPluginsByHook:\", error)": ".error(\"Caught error in getAllPluginsByHook:\", error)", ".error(\n          `Document count mismatch! Original: ${documents.length}, Distributed: ${totalDistributed}`\n        )": ".error(\n          `Document count mismatch! Original: ${documents.length}, Distributed: ${totalDistributed}`\n        )", ".error(`Error saving database:`, error)": ".error(`Error saving database:`, error)", ".error(`Error loading database:`, error)": ".error(`Error loading database:`, error)", ".error(`Error clearing storage:`, error)": ".error(`Error clearing storage:`, error)", ".error(\"Error getting vector length:\", error)": ".error(\"Error getting vector length:\", error)", ".error(`Error initializing Orama database:`, error)": ".error(`Error initializing Orama database:`, error)", ".error(`Error saving Orama database:`, error)": ".error(`Error saving Orama database:`, error)", ".error(\"Error clearing the local Copilot index:\", err)": ".error(\"Error clearing the local Copilot index:\", err)", ".error(\"Error deleting document from local Copilotindex:\", err)": ".error(\"Error deleting document from local Copilotindex:\", err)", ".error(\"Error getting latest file mtime from VectorDB:\", err)": ".error(\"Error getting latest file mtime from VectorDB:\", err)", ".error(\n          `Failed to ${existingDoc.hits.length > 0 ? \"update\" : \"insert\"} document ${docToSave.id}:`,\n          insertErr\n        )": ".error(\n          `Failed to ${existingDoc.hits.length > 0 ? \"update\" : \"insert\"} document ${docToSave.id}:`,\n          insertErr\n        )", ".error(\"Failed to restore previous document version:\", restoreErr)": ".error(\"Failed to restore previous document version:\", restoreErr)", ".error(`Error upserting document ${docToSave.id}:`, err)": ".error(`Error upserting document ${docToSave.id}:`, err)", ".error(\n        \"Orama database not found. Please make sure you have a working embedding model.\"\n      )": ".error(\n        \"Orama database not found. Please make sure you have a working embedding model.\"\n      )", ".error(\"Error garbage collecting the Copilot index:\", err)": ".error(\"Error garbage collecting the Copilot index:\", err)", ".error(\"Error getting indexed files:\", err)": ".error(\"Error getting indexed files:\", err)", ".error(\"Error checking if database is empty:\", err)": ".error(\"Error checking if database is empty:\", err)", ".error(\"Embedding instance not found.\")": ".error(\"Embedding instance not found.\")", ".error(\"Fatal error during indexing:\", error)": ".error(\"Fatal error during indexing:\", error)", ".error(`Error reindexing file ${file.path}:`, error)": ".error(`Error reindexing file ${file.path}:`, error)", ".error(\"Failed to initialize vector store:\", error)": ".error(\"Failed to initialize vector store:\", error)", ".error(\"Error in rewriteQuery:\", error)": ".error(\"Error in rewriteQuery:\", error)", ".error(\n        \"Error in convertQueryToVector, please ensure your embedding model is working and has an adequate context length:\",\n        error,\n        \"\\nQuery:\",\n        query\n      )": ".error(\n        \"Error in convertQueryToVector, please ensure your embedding model is working and has an adequate context length:\",\n        error,\n        \"\\nQuery:\",\n        query\n      )", ".error(\"Error indexing vault:\", error)": ".error(\"Error indexing vault:\", error)", ".error(`Error processing web search query ${query}:`, error)": ".error(`Error processing web search query ${query}:`, error)", ".error(`Error transcribing YouTube video ${url}:`, error)": ".error(`Error transcribing YouTube video ${url}:`, error)", ".error(\"Error in intent analysis:\", error)": ".error(\"Error in intent analysis:\", error)", ".error(\"Error during LLM invocation:\", error)": ".error(\"Error during LLM invocation:\", error)", ".error(errorData)": ".error(errorData)", ".error(\"Chain is not initialized properly, re-initializing chain: \", getChainType()": ".error(\"Chain is not initialized properly, re-initializing chain: \", getChainType()", ".error(\"Resetting default model. No model configuration found for: \", newModelKey)": ".error(\"Resetting default model. No model configuration found for: \", newModelKey)", ".error(\"createChainWithNewModel failed: \", error)": ".error(\"createChainWithNewModel failed: \", error)", ".error(\"setChain failed: No chat model set.\")": ".error(\"<PERSON><PERSON><PERSON><PERSON> failed: No chat model set.\")", ".error(\"Error searching DB:\", error)": ".error(\"Error searching DB:\", error)", ".error(\"Error counting tokens: \", error)": ".error(\"Error counting tokens: \", error)", ".error(\"Error indexing vault to Copilot index:\", err)": ".error(\"Error indexing vault to Copilot index:\", err)", ".error(\"Error re-indexing vault to Copilot index:\", err)": ".error(\"Error re-indexing vault to Copilot index:\", err)", ".error(\"Error listing indexed files:\", error)": ".error(\"Error listing indexed files:\", error)", ".error(\"Error removing files from index:\", err)": ".error(\"Error removing files from index:\", err)", ".error(\"aria-hidden\", target, \"in not contained inside\", parent, \". Doing nothing\")": ".error(\"aria-hidden\", target, \"in not contained inside\", parent, \". Doing nothing\")", ".error(\"aria-hidden: cannot operate on \", node, e3)": ".error(\"aria-hidden: cannot operate on \", node, e3)", ".error(\"Error refreshing vault index:\", error)": ".error(\"Error refreshing vault index:\", error)", ".error(`Error processing embedded PDF ${pdfName}:`, error)": ".error(`Error processing embedded PDF ${pdfName}:`, error)", ".error(`Error processing file ${note.path}:`, error)": ".error(`Error processing file ${note.path}:`, error)", ".error(\"Model request failed:\", error)": ".error(\"Model request failed:\", error)", ".error(`Error processing URL ${url}:`, error)": ".error(`Error processing URL ${url}:`, error)", ".error(\"App instance is not available.\")": ".error(\"App instance is not available.\")", ".error(\"Error regenerating message:\", error)": ".error(\"Error regenerating message:\", error)", ".error(\"Error reloading plugin:\", error)": ".error(\"Error reloading plugin:\", error)", ".error(`Error extracting content from PDF ${file.path}:`, error)": ".error(`Error extracting content from PDF ${file.path}:`, error)", ".error(\"Failed to check for updates:\", error)": ".error(\"Failed to check for updates:\", error)", "name: `${method}.${payload.id}`": "name: `${method}.${payload.id}`", "name: `${method}.${payload.id}.${key}`": "name: `${method}.${payload.id}.${key}`", "name: `attachment.${payload.id}.${name}`": "name: `attachment.${payload.id}.${name}`", "name: \"start\"": "name: \"start\"", "name: \"end\"": "name: \"end\"", "name: \"error\"": "name: \"error\"", "name: \"agent_action\"": "name: \"agent_action\"", "name: \"agent_end\"": "name: \"agent_end\"", "name: \"text\"": "name: \"text\"", "name: \"new_token\"": "name: \"new_token\"", "name: \"<runnable_lambda>\"": "name: \"<runnable_lambda>\"", "name : `${run.name}:${count5}`": "name : `${run.name}:${count5}`", "name: `${this.getName()}Input`": "name: `${this.getName()}Input`", "name: `${this.getName()}Output`": "name: `${this.getName()}Output`", "name: \"mustache.js\"": "name: \"mustache.js\"", "name : \"\";\n          var syntheticFrame = name ? describeBuiltInComponentFrame(name) : \"": "name : \"\";\n          var syntheticFrame = name ? describeBuiltInComponentFrame(name) : \"", "name: \"Anno Domini\"": "name: \"公元\"", "name: \"Before Christ\"": "name: \"公元前\"", "name: \"hostHeaderMiddleware\"": "name: \"hostHeaderMiddleware\"", "name: \"loggerMiddleware\"": "name: \"loggerMiddleware\"", "name: \"recursionDetectionMiddleware\"": "name: \"recursionDetectionMiddleware\"", "name : `${pathname}/`": "name : `${pathname}/`", "name: \"aws\"": "name: \"aws\"", "name: \"aws-cn\"": "name: \"aws-cn\"", "name: \"aws-us-gov\"": "name: \"aws-us-gov\"", "name: \"aws-iso\"": "name: \"aws-iso\"", "name: \"aws-iso-b\"": "name: \"aws-iso-b\"", "name: \"aws-iso-e\"": "name: \"aws-iso-e\"", "name: \"aws-iso-f\"": "name: \"aws-iso-f\"", "name: \"getUserAgentMiddleware\"": "name: \"getUserAgentMiddleware\"", "name: \"deserializerMiddleware\"": "name: \"deserializerMiddleware\"", "name: \"serializerMiddleware\"": "name: \"serializerMiddleware\"", "name: \"endpointV2Middleware\"": "name: \"endpointV2Middleware\"", "name: \"httpAuthSchemeMiddleware\"": "name: \"httpAuthSchemeMiddleware\"", "name: \"retryMiddleware\"": "name: \"retryMiddleware\"", "name: \"httpSigningMiddleware\"": "name: \"httpSigningMiddleware\"", "name: \"contentLengthMiddleware\"": "name: \"contentLengthMiddleware\"", "name: \"sigv4\"": "name: \"sigv4\"", "name: \"cognito-identity\"": "name: \"cognito-identity\"", "name: \"useFipsEndpoint\"": "name: \"useFipsEndpoint\"", "name: \"endpoint\"": "name: \"endpoint\"", "name: \"region\"": "name: \"region\"", "name: \"useDualstackEndpoint\"": "name: \"useDualstackEndpoint\"", "name: \"@aws-sdk/client-cognito-identity\"": "name: \"@aws-sdk/client-cognito-identity\"", "name: \"AWS SDK for JavaScript Team\"": "name: \"AWS SDK for JavaScript Team\"", "name: \"SHA-256\"": "name: \"SHA-256\"", "name: \"HMAC\"": "name: \"HMAC\"", "name: \"Googlebot\"": "name: \"Google<PERSON>\"", "name: \"Opera\"": "name: \"Opera\"", "name: \"Samsung Internet for Android\"": "name: \"三星互联网浏览器 for Android\"", "name: \"NAVER Whale Browser\"": "name: \"NAVER 鲸鱼浏览器\"", "name: \"MZ Browser\"": "name: \"MZ 浏览器\"", "name: \"Focus\"": "name: \"Focus\"", "name: \"Swing\"": "name: \"<PERSON>\"", "name: \"Opera Coast\"": "name: \"Opera Coast\"", "name: \"Opera Touch\"": "name: \"Opera Touch\"", "name: \"Yandex Browser\"": "name: \"Yandex 浏览器\"", "name: \"UC Browser\"": "name: \"UC 浏览器\"", "name: \"Maxthon\"": "name: \"<PERSON><PERSON><PERSON>\"", "name: \"Epiphany\"": "name: \"<PERSON><PERSON><PERSON><PERSON>\"", "name: \"Puffin\"": "name: \"<PERSON><PERSON>in\"", "name: \"Sleipnir\"": "name: \"<PERSON><PERSON><PERSON><PERSON>\"", "name: \"K-Meleon\"": "name: \"K-<PERSON><PERSON>\"", "name: \"WeChat\"": "name: \"微信\"", "name: \"Internet Explorer\"": "name: \"Internet Explorer\"", "name: \"Microsoft Edge\"": "name: \"微软 Edge\"", "name: \"Vivaldi\"": "name: \"<PERSON><PERSON><PERSON>\"", "name: \"SeaMonkey\"": "name: \"SeaMon<PERSON>\"", "name: \"Sailfish\"": "name: \"<PERSON><PERSON><PERSON>\"", "name: \"Amazon Silk\"": "name: \"亚马逊 Silk\"", "name: \"PhantomJS\"": "name: \"PhantomJS\"", "name: \"SlimerJS\"": "name: \"SlimerJS\"", "name: \"BlackBerry\"": "name: \"<PERSON><PERSON><PERSON>\"", "name: \"WebOS Browser\"": "name: \"WebOS 浏览器\"", "name: \"Bada\"": "name: \"<PERSON><PERSON>\"", "name: \"Tizen\"": "name: \"<PERSON>ize<PERSON>\"", "name: \"QupZilla\"": "name: \"QupZ<PERSON>\"", "name: \"Firefox\"": "name: \"Firefox\"", "name: \"Electron\"": "name: \"<PERSON><PERSON><PERSON>\"", "name: \"Miui\"": "name: \"<PERSON><PERSON>\"", "name: \"Chromium\"": "name: \"<PERSON>romium\"", "name: \"Chrome\"": "name: \"Chrome\"", "name: \"Google Search\"": "name: \"Google 搜索\"", "name: \"Android Browser\"": "name: \"Android 浏览器\"", "name: \"PlayStation 4\"": "name: \"PlayStation 4\"", "name: \"Safari\"": "name: \"<PERSON><PERSON>\"", "name: \"InternalErrorException\"": "name: \"InternalErrorException\"", "name: \"InvalidParameterException\"": "name: \"InvalidParameterException\"", "name: \"LimitExceededException\"": "name: \"LimitExceededException\"", "name: \"NotAuthorizedException\"": "name: \"NotAuthorizedException\"", "name: \"ResourceConflictException\"": "name: \"ResourceConflictException\"", "name: \"TooManyRequestsException\"": "name: \"TooManyRequestsException\"", "name: \"ResourceNotFoundException\"": "name: \"ResourceNotFoundException\"", "name: \"ExternalServiceException\"": "name: \"ExternalServiceException\"", "name: \"InvalidIdentityPoolConfigurationException\"": "name: \"InvalidIdentityPoolConfigurationException\"", "name: \"DeveloperUserAlreadyRegisteredException\"": "name: \"DeveloperUserAlreadyRegisteredException\"", "name: \"ConcurrentModificationException\"": "name: \"ConcurrentModificationException\"", "name: \"sts\"": "name: \"sts\"", "name: \"useGlobalEndpoint\"": "name: \"useGlobalEndpoint\"", "name: \"@aws-sdk/client-sts\"": "name: \"@aws-sdk/client-sts\"", "name: \"ExpiredTokenException\"": "name: \"ExpiredTokenException\"", "name: \"MalformedPolicyDocumentException\"": "name: \"MalformedPolicyDocumentException\"", "name: \"PackedPolicyTooLargeException\"": "name: \"PackedPolicyTooLargeException\"", "name: \"RegionDisabledException\"": "name: \"RegionDisabledException\"", "name: \"IDPRejectedClaimException\"": "name: \"IDPRejectedClaimException\"", "name: \"InvalidIdentityTokenException\"": "name: \"InvalidIdentityTokenException\"", "name: \"IDPCommunicationErrorException\"": "name: \"IDPCommunicationErrorException\"", "name: \"InvalidAuthorizationMessageException\"": "name: \"InvalidAuthorizationMessageException\"", "name : \"\";\n            return `${ctor} {}`.trim();\n          } else {\n            return replacement.toString();\n          }\n        });\n      },\n      inspect(value) {\n        switch (typeof value) {\n          case \"": "name : \"\";\n            return `${ctor} {}`.trim();\n          } else {\n            return replacement.toString();\n          }\n        });\n      },\n      inspect(value) {\n        switch (typeof value) {\n          case \"", "name : \" (anonymous)\"": "name : \" (anonymous)\"", "name: \"name\"": "name: \"name\"", "name: \"api-test\"": "name: \"api-test\"", "name: \"en\"": "name: \"en\"", "name: `%s`": "name: `%s`", "name: \"className\"": "name: \"<PERSON><PERSON><PERSON>\"", "name: \"attributeName\"": "name: \"<PERSON><PERSON><PERSON>\"", "name: \"glyphName\"": "name: \"glyph<PERSON><PERSON>\"", "name : \"invokeguardedcallback\"": "name : \"invokeguardedcallback\"", "name: \"gpt-4o\"": "name: \"gpt-4o\"", "name: \"gpt-4o-mini\"": "name: \"gpt-4o-mini\"", "name: \"gpt-4-turbo\"": "name: \"gpt-4-turbo\"", "name: \"claude-3-5-sonnet-latest\"": "name: \"claude-3-5-sonnet-latest\"", "name: \"claude-3-5-haiku-latest\"": "name: \"claude-3-5-haiku-latest\"", "name: \"command-r\"": "name: \"command-r\"", "name: \"command-r-plus\"": "name: \"command-r-plus\"", "name: \"gemini-1.5-pro\"": "name: \"gemini-1.5-pro\"", "name: \"gemini-1.5-flash\"": "name: \"gemini-1.5-flash\"", "name: \"azure-openai\"": "name: \"azure-openai\"", "name: \"copilot-plus-small\"": "name: \"copilot-plus-small\"", "name: \"copilot-plus-multilingual\"": "name: \"copilot-plus-multilingual\"", "name: \"text-embedding-3-small\"": "name: \"text-embedding-3-small\"", "name: \"text-embedding-3-large\"": "name: \"text-embedding-3-large\"", "name: \"embed-multilingual-light-v3.0\"": "name: \"embed-multilingual-light-v3.0\"", "name: \"text-embedding-004\"": "name: \"text-embedding-004\"", "name: \"' + name + '\"": "name: \"' + name + '\"", "name: \"localSearch\"": "name: \"localSearch\"", "name: \"indexVault\"": "name: \"indexVault\"", "name: \"webSearch\"": "name: \"webSearch\"", "name: \"getCurrentTime\"": "name: \"getCurrentTime\"", "name: \"getTimeRangeMs\"": "name: \"getTimeRangeMs\"", "name: \"getTimeInfoByEpoch\"": "name: \"getTimeInfoByEpoch\"", "name: \"startPomodoro\"": "name: \"startPomodoro\"", "name: \"youtubeTranscription\"": "name: \"youtubeTranscription\"", "name: \"English\"": "name: \"英语\"", "name: \"Chinese\"": "name: \"中文\"", "name: \"Japanese\"": "name: \"Japanese\"", "name: \"Korean\"": "name: \"Korean\"", "name: \"Spanish\"": "name: \"Spanish\"", "name: \"French\"": "name: \"French\"", "name: \"German\"": "name: \"German\"", "name: \"Italian\"": "name: \"Italian\"", "name: \"Portuguese\"": "name: \"Portuguese\"", "name: \"Russian\"": "name: \"Russian\"", "name: \"Arabic\"": "name: \"Arabic\"", "name: \"Bengali\"": "name: \"Bengali\"", "name: \"Czech\"": "name: \"Czech\"", "name: \"Danish\"": "name: \"Danish\"", "name: \"Greek\"": "name: \"Greek\"", "name: \"Finnish\"": "name: \"Finnish\"", "name: \"Hebrew\"": "name: \"Hebrew\"", "name: \"Hindi\"": "name: \"Hindi\"", "name: \"Hungarian\"": "name: \"Hungarian\"", "name: \"Indonesian\"": "name: \"Indonesian\"", "name: \"Malay\"": "name: \"Malay\"", "name: \"Dutch\"": "name: \"Dutch\"", "name: \"Norwegian\"": "name: \"Norwegian\"", "name: \"Polish\"": "name: \"Polish\"", "name: \"Swedish\"": "name: \"Swedish\"", "name: \"Thai\"": "name: \"Thai\"", "name: \"Turkish\"": "name: \"Turkish\"", "name: \"Ukrainian\"": "name: \"Ukrainian\"", "name: \"Vietnamese\"": "name: \"Vietnamese\"", "name: \"Afrikaans\"": "name: \"Afrikaans\"", "name: \"Bulgarian\"": "name: \"Bulgarian\"", "name: \"Catalan\"": "name: \"Catalan\"", "name: \"Estonian\"": "name: \"Estonian\"", "name: \"Persian\"": "name: \"Persian\"", "name: \"Filipino\"": "name: \"Filipino\"", "name: \"Croatian\"": "name: \"Croatian\"", "name: \"Icelandic\"": "name: \"Icelandic\"", "name: \"Lithuanian\"": "name: \"Lithuanian\"", "name: \"Latvian\"": "name: \"Latvian\"", "name: \"Romanian\"": "name: \"Romanian\"", "name: \"Slovak\"": "name: \"Slovak\"", "name: \"Slovenian\"": "name: \"Slovenian\"", "name: \"Serbian\"": "name: \"Serbian\"", "name: \"Swahili\"": "name: \"Swahili\"", "name: \"Tamil\"": "name: \"Tamil\"", "name: \"Telugu\"": "name: \"Telugu\"", "name: \"Urdu\"": "name: \"Urdu\"", "name: \"Zulu\"": "name: \"<PERSON><PERSON>\"", "name: \"Mongolian\"": "name: \"Mongolian\"", "name: \"Nepali\"": "name: \"Nepali\"", "name: \"Punjabi\"": "name: \"Punjabi\"", "name: \"Sinhala\"": "name: \"Sinhala\"", "name: \"arrow\"": "name: \"arrow\"", "name: \"flip\"": "name: \"flip\"", "name: \"hide\"": "name: \"hide\"", "name: \"offset\"": "name: \"offset\"", "name: \"shift\"": "name: \"shift\"", "name: \"size\"": "name: \"size\"", "name: \"transformOrigin\"": "name: \"<PERSON><PERSON><PERSON><PERSON>\"", "description: \"Africa (Cape Town)\"": "description: \"Africa (Cape Town)\"", "description: \"Asia Pacific (Hong Kong)\"": "description: \"Asia Pacific (Hong Kong)\"", "description: \"Asia Pacific (Tokyo)\"": "description: \"Asia Pacific (Tokyo)\"", "description: \"Asia Pacific (Seoul)\"": "description: \"Asia Pacific (Seoul)\"", "description: \"Asia Pacific (Osaka)\"": "description: \"Asia Pacific (Osaka)\"", "description: \"Asia Pacific (Mumbai)\"": "description: \"Asia Pacific (Mumbai)\"", "description: \"Asia Pacific (Hyderabad)\"": "description: \"Asia Pacific (Hyderabad)\"", "description: \"Asia Pacific (Singapore)\"": "description: \"Asia Pacific (Singapore)\"", "description: \"Asia Pacific (Sydney)\"": "description: \"Asia Pacific (Sydney)\"", "description: \"Asia Pacific (Jakarta)\"": "description: \"Asia Pacific (Jakarta)\"", "description: \"Asia Pacific (Melbourne)\"": "description: \"Asia Pacific (Melbourne)\"", "description: \"Asia Pacific (Malaysia)\"": "description: \"Asia Pacific (Malaysia)\"", "description: \"AWS Standard global region\"": "description: \"AWS Standard global region\"", "description: \"Canada (Central)\"": "description: \"Canada (Central)\"", "description: \"Canada West (Calgary)\"": "description: \"Canada West (Calgary)\"", "description: \"Europe (Frankfurt)\"": "description: \"Europe (Frankfurt)\"", "description: \"Europe (Zurich)\"": "description: \"Europe (Zurich)\"", "description: \"Europe (Stockholm)\"": "description: \"Europe (Stockholm)\"", "description: \"Europe (Milan)\"": "description: \"Europe (Milan)\"", "description: \"Europe (Spain)\"": "description: \"Europe (Spain)\"", "description: \"Europe (Ireland)\"": "description: \"Europe (Ireland)\"", "description: \"Europe (London)\"": "description: \"Europe (London)\"", "description: \"Europe (Paris)\"": "description: \"Europe (Paris)\"", "description: \"Israel (Tel Aviv)\"": "description: \"Israel (Tel Aviv)\"", "description: \"Middle East (UAE)\"": "description: \"Middle East (UAE)\"", "description: \"Middle East (Bahrain)\"": "description: \"Middle East (Bahrain)\"", "description: \"South America (Sao Paulo)\"": "description: \"South America (Sao Paulo)\"", "description: \"US East (N. Virginia)\"": "description: \"US East (N. Virginia)\"", "description: \"US East (Ohio)\"": "description: \"US East (Ohio)\"", "description: \"US West (N. California)\"": "description: \"US West (N. California)\"", "description: \"US West (Oregon)\"": "description: \"US West (Oregon)\"", "description: \"AWS China global region\"": "description: \"AWS China global region\"", "description: \"China (Beijing)\"": "description: \"China (Beijing)\"", "description: \"China (Ningxia)\"": "description: \"China (Ningxia)\"", "description: \"AWS GovCloud (US) global region\"": "description: \"AWS GovCloud (US) global region\"", "description: \"AWS GovCloud (US-East)\"": "description: \"AWS GovCloud (US-East)\"", "description: \"AWS GovCloud (US-West)\"": "description: \"AWS GovCloud (US-West)\"", "description: \"AWS ISO (US) global region\"": "description: \"AWS ISO (US) global region\"", "description: \"US ISO East\"": "description: \"US ISO East\"", "description: \"US ISO WEST\"": "description: \"US ISO WEST\"", "description: \"AWS ISOB (US) global region\"": "description: \"AWS ISOB (US) global region\"", "description: \"US ISOB East (Ohio)\"": "description: \"US ISOB East (Ohio)\"", "description: \"EU ISOE West\"": "description: \"EU ISOE West\"", "description: \"AWS SDK for JavaScript Cognito Identity Client for Node.js, Browser and React Native\"": "description: \"AWS SDK for JavaScript Cognito Identity 客户端（适用于 Node.js、浏览器和 React Native）\"", "description: \"AWS SDK for JavaScript Sts Client for Node.js, Browser and React Native\"": "description: \"AWS SDK for JavaScript Sts 客户端（适用于 Node.js、浏览器和 React Native）\"", "description: \"Search for notes based on the time range and query\"": "description: \"根据时间范围和查询条件搜索笔记\"", "description: \"Index the vault to the Copilot index\"": "description: \"将库索引到 Copilot 索引\"", "description: \"Search the web for information\"": "description: \"搜索网络信息\"", "description: \"Get the current time in various formats, including timezone information\"": "description: \"获取当前时间，包括多种格式和时区信息\"", "description: \"Get a time range in milliseconds based on a natural language time expression\"": "description: \"根据自然语言时间表达式获取以毫秒为单位的时间范围\"", "description: \"Convert a Unix timestamp (in seconds or milliseconds) to detailed time information\"": "description: \"将 Unix 时间戳（以秒或毫秒为单位）转换为详细的时间信息\"", "description: \"Start a Pomodoro timer with a customizable interval\"": "description: \"启动一个可自定义间隔的番茄钟计时器\"", "description: \"Get the transcript of a YouTube video\"": "description: \"获取 YouTube 视频的字幕\"", "description: \"The default folder name where chat conversations will be saved. Default is 'copilot-conversations'\"": "description: \"默认的文件夹名称,聊天对话将保存在此处。默认是 'copilot-conversations'\"", "description: \"The default folder name where custom prompts will be saved. Default is 'copilot-custom-prompts'\"": "description: \"自定义聊天提示词将保存的默认文件夹名称。默认是 'copilot-custom-prompts'\"", "description: \"The default tag to be used when saving a conversation. Default is 'ai-conversations'\"": "description: \"保存对话时使用的默认标签。默认是'ai-conversations'\"", "description: \"Automatically save the chat when starting a new one or when the plugin reloads\"": "description: \"在开始新聊天或插件重新加载时自动保存聊天\"", "description: \"Show suggested prompts in the chat view\"": "description: \"在聊天视图中显示建议提示\"", "description: \"Show relevant notes in the chat view\"": "description: \"在聊天视图中显示相关笔记\"", "description: \"This is your actual model, no need to pass a model name separately.\"": "description: \"这是你的实际模型,无需单独传递模型名称。\"", "description: \"Default is 0.1. Higher values will result in more creativeness, but also more mistakes. Set to 0 for no randomness.\"": "description: \"默认是 0.1。更高值的温度参数将导致更多的创造性,但也可能带来更多错误。设置为 0 表示无随机性。\"", "description: \"The number of previous conversation turns to include in the context. Default is 15 turns, i.e. 30 messages.\"": "description: \"要包含在上下文中的先前对话轮数。默认是 15 轮,即 30 条消息。\"", "description: \"Customize the system prompt for all messages, may result in unexpected behavior!\"": "description: \"自定义所有消息的聊天提示词，可能会导致意外行为！\"", "description: \"Copilot goes through your vault to find relevant blocks and passes the top N blocks to the LLM. Default for N is 3. Increase if you want more sources included in the answer generation step.\"": "description: \"Copilot会遍历您的知识库，查找相关块，并将前N个块传递给LLM。N的默认值为3。如果您希望在答案生成步骤中包含更多来源，请增加N的值。\"", "description: \"Default is 10. Decrease if you are rate limited by your embedding provider.\"": "description: \"默认值为10。若您的embedding提供商对速率进行了限制，请相应减少该值。\"", "description: \"Number of partitions for Copilot index. Default is 1. Increase if you have issues indexing large vaults. Warning: Changes require clearing and rebuilding the index!\"": "description: \"Copilot索引的分区数。默认值为1。如果您在索引大型保管库时遇到问题，请增加。警告：更改需要清除并重建索引！\"", "description: \"If enabled, the index will be stored in the .obsidian folder and synced with Obsidian Sync by default. If disabled, it will be stored in .copilot-index folder at vault root.\"": "description: \"如果启用，索引将存储在.obsidian文件夹中，并默认与obsidian Sync同步。如果禁用，它将存储在vault根目录下的.copilot索引文件夹中。\"", "description: \"When enabled, Copilot index won't be loaded on mobile devices to save resources. Only chat mode will be available. Any existing index from desktop sync will be preserved. Uncheck to enable QA modes on mobile.\"": "description: \"启用后，Copilot 索引将不会在移动设备上加载以节省资源。仅聊天模式可用。从桌面同步的任何现有索引将被保留。取消勾选以在移动设备上启用问答模式。\"", "link: \"Blink\"": "link: \"Blink\"", "link: \"xmlnsXlink\"": "link: \"xmlnsXlink\"", "text: \"{\"": "text: \"{\"", "text: \"}\"": "text: \"}\"", "text: \"text\"": "text: \"text\"", "text: \"tokenize me! :D\"": "text: \"tokenize me! :D\"", "text: \"\",\n          message: new AIMessageChunk({\n            content: \"": "text: \"\",\n          message: new AIMessageChunk({\n            content: \"", "text: \"\"\n        });\n      }\n      const chunk = new ChatGenerationChunk({\n        message,\n        text: choice.delta.content ?? \"": "text: \"\"\n        });\n      }\n      const chunk = new ChatGenerationChunk({\n        message,\n        text: choice.delta.content ?? \"", "text: \"\"\n      });\n    }\n    if (options.signal?.aborted) {\n      throw new Error(\"": "text: \"\"\n      });\n    }\n    if (options.signal?.aborted) {\n      throw new Error(\"", "text: \"\",\n      message: new AIMessageChunk({\n        content: \"": "text: \"\",\n      message: new AIMessageChunk({\n        content: \"", "text: \"\"\n      });\n      yield generationChunk;\n    }\n    if (options.signal?.aborted) {\n      throw new Error(\"": "text: \"\"\n      });\n      yield generationChunk;\n    }\n    if (options.signal?.aborted) {\n      throw new Error(\"", "text: '${this.text}'": "text: '${this.text}'", "text: \"User Custom Prompt\"": "text: \"用户自定义聊天提示词\"", "text: \"Title\"": "text: \"标题\"", "text: \"The title of the prompt, must be unique.\"": "text: \"聊天提示词的标题,必须是唯一的。\"", "text: \"Prompt\"": "text: \"聊天提示词\"", "text: \"- {} represents the selected text (not required). \"": "text: \"- {} 代表所选文本(非必需)。\"", "text: \"- {[[Note Title]]} represents a note. \"": "text: \"- {[[笔记标题]]} 代表一个笔记。\"", "text: \"- {activeNote} represents the active note. \"": "text: \"- {activeNote} 代表活动笔记。\"", "text: \"- {FolderPath} represents a folder of notes. \"": "text: \"- {FolderPath} 代表一个笔记文件夹。\"", "text: \"- {#tag1, #tag2} represents ALL notes with ANY of the specified tags in their property (an OR operation). \"": "text: \"- {#tag1, #tag2} 代表所有具有指定标签中的任何一个标签的笔记(或操作)。\"", "text: \"Apply custom prompt to selection.\"": "text: \"将自定义聊天提示词应用于选择。\"", "text: \"awesome chatGPT prompts\"": "text: \"超赞的 chatGPT 聊天提示词\"", "text: \"Save\"": "text: \"保存\"", "text: \"Inspect Copilot Index by Note Paths\"": "text: \"按笔记路径检查 Copilot 索引\"", "text: \"Show Index Data\"": "text: \"显示索引数据\"", "text: \"Remove Files from Copilot Index\"": "text: \"从 Copilot 索引中移除文件\"", "text: \"reference\"": "text: \"reference\"", "text: \"Sources\"": "text: \"Sources\"", "text: \"High Relevance Sources\"": "text: \"High Relevance Sources\"", "text: \"Lower Relevance Sources\"": "text: \"Lower Relevance Sources\"", "search: \"Google Search\"": "search: \"谷歌搜索\"", "page: \"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-cognito-identity\"": "page: \"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-cognito-identity\"", "page: \"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sts\"": "page: \"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sts\"", ".setButtonText(\"Remove\")": ".setButtonText(\"Remove\")", ".setName(\"File paths\")": ".setName(\"File paths\")", ".setDesc(\n      \"Paste the markdown list of file paths to remove from the index. You can get the list by running the command `List all indexed files`.\"\n    )": ".setDesc(\n      \"粘贴要从索引中删除的文件路径的 Markdown 列表。你可以通过运行命令 `列出所有已索引文件` 来获取该列表。\"\n    )", ".setPlaceholder(\"- [[path/to/file1.md]]\\n- [[path/to/file2.md]]\")": ".setPlaceholder(\"- [[path/to/file1.md]]\\n- [[path/to/file2.md]]\")", ".appendText(\"Tip: turn on debug mode to show the processed prompt in the chat window.\")": ".appendText(\"Tip：打开调试模式，会在聊天窗口中显示已处理过的聊天提示词。\")", ".appendText(\n        \"Save the prompt to the local prompt library. You can then use it with the Copilot command: \"\n      )": ".appendText(\n        \"将聊天提示词保存到本地聊天提示词库，然后，您可以将其与Copilot命令一起使用：\"\n      )", ".appendText(\"Check out the \")": ".appendText(\"查看\")", ".appendText(\" for inspiration.\")": ".appendText(\"为获得灵感\")", "title: \"Copy\"": "title: \"复制\"", "title: \"Edit\"": "title: \"编辑\"", "title: \"Insert to note at cursor\"": "title: \"插入到笔记中的光标位置\"", "title: \"Regenerate\"": "title: \"重新生成\"", "Notice(\"Please fill in necessary fields!\")": "Notice(\"请填写必要字段！\")", "Notice(\"No active file\")": "Notice(\"无活动文档\")", "name: \"Fix grammar and spelling of selection\"": "name: \"修正选中内容的语法和拼写\"", "name: \"Summarize selection\"": "name: \"总结选中内容\"", "name: \"Generate table of contents for selection\"": "name: \"为选中内容生成目录\"", "name: \"Generate glossary for selection\"": "name: \"为选中内容生成术语表\"", "name: \"Simplify selection\"": "name: \"简化选中内容\"", "name: \"Emojify selection\"": "name: \"为选中内容添加表情符号\"", "name: \"Remove URLs from selection\"": "name: \"从选中内容中移除 URL\"", "name: \"Rewrite selection to a tweet\"": "name: \"将选中内容改写为推文\"", "name: \"Rewrite selection to a tweet thread\"": "name: \"将选中内容改写为推文串\"", "name: \"Make selection shorter\"": "name: \"使选中内容更简短\"", "name: \"Make selection longer\"": "name: \"使选中内容更长\"", "name: \"Explain selection like I'm 5\"": "name: \"像解释给五岁孩子一样解释选中内容\"", "name: \"Rewrite selection to a press release\"": "name: \"将选中内容改写为新闻稿\"", "name: \"Translate selection\"": "name: \"翻译选中内容\"", "name: \"Change tone of selection\"": "name: \"改变选中内容的语气\"", "name: \"Count words and tokens in selection\"": "name: \"计算选中内容的单词和Token数\"", "name: \"Count total tokens in your vault\"": "name: \"计算保管库(Vault)中的总Token数\"", "name: \"Model Name\"": "name: \"模型名称\"", "name: \"Provider\"": "name: \"提供商\"", "name: \"Base URL (optional)\"": "name: \"基础 URL(可选)\"", "name: \"API Key (optional)\"": "name: \"API 密钥(可选)\"", "name: \"User System Prompt\"": "name: \"用户系统聊天提示词\"", "name: \"Default Conversation Folder Name\"": "name: \"默认对话文件夹名称\"", "name: \"Default Conversation Tag\"": "name: \"默认对话标签\"", "name: \"Autosave Chat\"": "name: \"自动保存聊天\"", "name: \"Custom Prompts Folder Name\"": "name: \"自定义聊天提示词文件夹名称\"", "name: \"Temperature\"": "name: \"温度参数\"", "name: \"Token limit\"": "name: \"Token限制\"", "name: \"Conversation turns in context\"": "name: \"上下文中的对话轮次\"", "name: \"Auto-index vault strategy\"": "name: \"自动索引保管库(Vault)策略\"", "name: \"Max Sources\"": "name: \"最大来源\"", "name: \"Requests per second\"": "name: \"每秒请求数\"", "name: \"Number of Partitions\"": "name: \"分区数量\"", "name: \"Exclusions\"": "name: \"排除不需要的项目\"", "name: \"Inclusions\"": "name: \"指定需要包含的项目\"", "name: \"Enable Obsidian Sync for Copilot index\"": "name: \"为Copilot索引启用obsidian同步\"", "name: \"Disable index loading on mobile\"": "name: \"禁用移动设备上的索引加载\"", "name: \"Toggle Copilot Chat Window\"": "name: \"切换 Copilot 聊天窗口\"", "name: \"Add custom prompt\"": "name: \"添加自定义聊天提示词\"", "name: \"Apply custom prompt\"": "name: \"应用自定义聊天提示词\"", "name: \"Apply ad-hoc custom prompt\"": "name: \"应用临时自定义聊天提示词\"", "name: \"Delete custom prompt\"": "name: \"删除自定义聊天提示词\"", "name: \"Edit custom prompt\"": "name: \"编辑自定义聊天提示词\"", "name: \"Index (refresh) vault for QA\"": "name: \"为 QA 索引(刷新)保管库(Vault)\"", "name: \"Force re-index vault for QA\"": "name: \"强制重新索引保管库(Vault)用于 QA\"", "name: \"Load Copilot Chat conversation\"": "name: \"加载 Copilot 聊天对话\"", "description: `The name of the model, i.e. ${isEmbeddingModel ? \"text-embedding-3-small\" : \"gpt-4o-mini\"}`": "description: `模型的名称,例如 ${isEmbeddingModel ? 'text-embedding-3-small' : 'gpt-4o-mini'}`", "description: \"For 3rd party OpenAI Format endpoints only. Leave blank for other providers.\"": "description: \"仅适用于第三方 OpenAI 格式端点。对于其他提供商请留空。\"", "description: \"API key for the 3rd party provider\"": "description: \"第三方提供商的 API 密钥\"", "description: \"(Optional) For embedding provider Azure OpenAI\"": "description: \"(可选)对于嵌入提供商 Azure OpenAI\"", "description: \"Decide when you want the vault to be indexed.\"": "description: \"决定何时对保管库(Vault)进行索引。\"", "description: \"Copilot goes through your vault to find relevant blocks and passes the top N blocks to the LLM. Default for N is 3. Increase if you want more sources included in the answer generation step. WARNING: more sources significantly degrades answer quality if the chat model is weak!\"": "description: \"Copilot通过你的保险库找到相关的区块，并将前N个区块传递给LLM。N的默认值为3。如果您希望在答案生成步骤中包含更多来源，请增加。警告：如果聊天模型较弱，更多的来源会显著降低回答质量！\"", "text: \"Additional Settings\"": "text: \"附加设置\"", ".setName(\"Enable Encryption\")": ".setName(\"启用加密\")", ".setName(\"Debug mode\")": ".setName(\"调试模式\")", ".appendText(\"Enable encryption for the API keys.\")": ".appendText(\"为API密钥启用加密\")", ".appendText(\"Debug mode will log all API requests and prompts to the console.\")": ".appendText(\"调试模式会将所有的API请求和聊天提示词都记录到控制台\")", ".setTitle(\"Copilot: Summarize Selection\")": ".setTitle(\"Copilot: 总结选中内容\")", "Notice(\"Index does not exist, indexing vault for similarity search...\")": "Notice(\"索引不存在，正在为保管库(Vault)建立索引以进行相似性搜索...\")", "name: \"Find similar notes to active note\"": "name: \"查找与活动文档类似的其它文档\"", "text: \"Similar Note Blocks to Current Note\"": "text: \"与当前文档类似的文档块\"", "Notice(`Indexing completed with errors. Check the console for details.`)": "Notice(`索引已完成，但有错误。请查看控制台了解详细信息。`)", "Notice(\n          \"An unexpected error occurred while indexing the vault. Please check the console for details.\"\n        )": "Notice(\n          \"为保管库(Vault)建立索引时发生意外错误。请检查控制台以了解详细信息。\"\n        )", "Notice(\"No documents to remove during garbage collection.\")": "Notice(\"在垃圾回收过程中没有要删除的文档。\")", "Notice(`Removed stale documents during garbage collection.`)": "Notice(`在垃圾回收过程中删除了过时的文档。`)", ".log(\"Copilot database initialized successfully.\")": ".log(\"Copilot数据库初始化成功。\")", ".log(\"Copilot index is disabled on mobile devices.\")": ".log(\"移动设备上的Copilot索引已禁用。\")", ".log(\"Index loading disabled on mobile device\")": ".log(\"移动设备上已禁用索引加载\")", ".log(`Config directory does not exist. Creating: ${configDir}`)": ".log(`配置目录不存在，正在创建:${configDir}`)", ".log(`Loaded existing Orama database for ${this.dbPath} from disk.`)": ".log(`已从磁盘加载${this.dbPath}的现有Orama数据库。`)", ".log(\n        `Schema mismatch detected. Rebuilding database with new vector length: ${currentVectorLength}`\n      )": ".log(\n        `检测到架构不匹配，使用新的向量长度重建数据库: ${currentVectorLength}`\n      )", ".log(`Saved Orama database to ${this.dbPath}.`)": ".log(`已将Orama数据库保存到${this.dbPath}.`)", "description: \"When specified, ONLY these paths, tags, or note titles will be indexed (comma separated). Files which were previously indexed will remain in the index unless you force re-index. If overlapping with exclusions, exclusions take precedence. Format: folder1, folder1/folder2, #tag1, #tag2, [[note1]], [[note2]]\"": "description: \"指定后，只有以下这些路径、标签或文档标题会被索引（请用逗号分隔开）。如果与排除项重叠，则排除项优先。格式：文件夹1、文件夹1/文件夹2、#标签1、#标签2、[[note1]]、[[note2]]\"", "Notice(\n              `Embedding error: please check your embedding model context length, and consider switching to a model with a larger context length: ${error.message}`\n            )": "Notice(\n              `嵌入错误：请检查您嵌入模型的上下文长度，并考虑切换到上下文长度更大的模型：${error.message}`\n            )", "Notice(`Setting model: ${modelConfig.modelName}`)": "Notice(`设置模型:${modelConfig.modelName}`)", "Notice(\"Error creating chain:\", error)": "Notice(\"创建LLM链时出错：\", error)", "Notice(`LangChain error: ${errorCode}`)": "Notice(`Lang<PERSON>hain错误:${errorCode}`)", "Notice(\"An error occurred while indexing vault to vector store.\")": "Notice(\"将保管库(Vault)索引到矢量存储时出错。\")", "Notice(\"Detected change in embedding model. Rebuilding vector store from scratch.\")": "Notice(\"检测到嵌入模型中的更改。从头开始重建矢量存储。\")", "Notice(\"Local vector store cleared successfully.\")": "Notice(\"本地矢量存储已成功清除。\")", "Notice(\"An error occurred while clearing the local vector store.\")": "Notice(\"清除本地矢量存储时出错。\")", "Notice(\"Local vector store garbage collected successfully.\")": "Notice(\"已成功收集本地矢量存储垃圾。\")", "Notice(\"An error occurred while garbage collecting the vector store.\")": "Notice(\"对矢量存储进行垃圾回收时出错。\")", "Notice(`Error: ${error.msg}. Please check your embedding model settings.`)": "Notice(`错误: ${error.msg}. 请检查您的嵌入模型设置。`)", "Notice(\n            \"An unexpected error occurred while setting up the QA chain. Please check the console for details.\"\n          )": "Notice(\n            \"设置QA链时发生意外错误，请检查控制台以了解详细信息。\"\n          )", "Notice(`${indexedFileCount} vault files indexed to vector store.`)": "Notice(`索引到矢量存储的${indexedFileCount}保管库(Vault)文件。`)", "Notice(`${indexedFileCount} vault files re-indexed to vector store.`)": "Notice(`${indexedFileCount}保管库(Vault)文件已重新索引到矢量存储。`)", "Notice(\"An error occurred while re-indexing vault to vector store.\")": "Notice(\"将保管库(Vault)重新索引到矢量存储时出错。\")", "Notice(\"An error occurred while saving vault to vector store.\")": "Notice(\"将保管库(Vault)保存到矢量存储时出错。\")", ".log(\"clearing chat memory\")": ".log(\"清除聊天记忆\")", ".log(\"Detected change in embedding model. Rebuilding vector store from scratch.\")": ".log(\"检测到嵌入模型中的更改，将从头开始重建矢量存储。\")", ".log(\"Local vector store cleared successfully, new instance created.\")": ".log(\"本地向量存储已成功清除，新实例已创建。\")", ".log(\"Local vector store garbage collected successfully.\")": ".log(\"已成功收集本地矢量存储垃圾。\")", ".log(`${indexedFileCount} vault files indexed to vector store.`)": ".log(`索引到矢量存储的${indexedFileCount}保管库(Vault)文件。`)", ".log(`${indexedFileCount} vault files re-indexed to vector store.`)": ".log(`${indexedFileCount}保管库(Vault)文件已重新索引到矢量存储。`)", "name: \"Clear local vector store\"": "name: \"清除本地向量存储\"", "name: \"Garbage collect vector store (remove files that no longer exist in vault)\"": "name: \"清理向量存储(移除保管库(Vault)中已不存在的文件)\"", "description: \"Warning: It will override the default system prompt for all messages!\"": "description: \"警告:这将覆盖所有消息的默认系统聊天提示词！\"", "description: \"When enabled, vector store index won't be loaded on mobile devices to save resources. Only chat mode will be available. Any existing index from desktop sync will be preserved.\"": "description: \"启用此功能后，矢量存储索引将不会在移动设备上加载，以节省资源。只有在聊天模式可用，来自桌面同步的任何现有索引都将被保留。\"", "text: \"Powerful AI agents\"": "text: \"强大的AI代理\"", "text: \"keeping all your data stored locally\"": "text: \"将所有数据存储在本地\"", "text: \"Stay Updated\"": "text: \"保持更新\"", "text: \"Alpha access spots are limited. \"": "text: \"Alpha接入点有限。\"", "text: \"prioritize supporters who have donated\"": "text: \"优先考虑已捐款的支持者\"", "text: \"donating now\"": "text: \"现在捐赠\"", "text: \"Learn More about Copilot Plus mode and join the waitlist here:\"": "text: \"了解更多关于Copilot Plus模式的信息，并在此处加入候补名单：\"", "name: \"Toggle Copilot Chat Window in Note Area\"": "name: \"在笔记区域切换 Copilot 聊天窗口\"", "), \"(Set with Copilot command: \"": "), \"(使用 Copilot 命令设置。: \"", "), \"set note context \"": "), \"设置笔记上下文\"", "), \"Default is active note)\"": "), \"默认是活动笔记)\"", "), \"If errors occur, please re-enter the API key, save and reload the plugin to see if it resolves the issue.\"": "), \"如果出现错误，请重新输入 API 密钥，保存并重载插件以查看是否解决了问题。\"", "), \"Your API key is stored locally and is only used to make requests to Google's services.\"": "), \"您的 API 密钥存储在本地，并且仅用于向 Google 的服务发起请求。\"", "), \"Your API key is stored locally and is only used to make requests to Anthropic's services.\"": "), \"您的 API 密钥存储在本地，并且仅用于向 Anthropic 的服务发起请求。\"", "), \"Your API key is stored locally and is only used to make requests to Groq's services.\"": "), \"您的 API 密钥存储在本地，并且仅用于向 Groq 的服务发起请求。\"", "), \" to generate. Default is 1000.\"": "), \" 生成的数量。默认是 1000.\"", "), \": Notes are never indexed to the vector store unless users run the command \"": "), \": 除非用户运行相应的命令，否则笔记不会被索引到向量数据库中。\"", "), \": Vault index is refreshed on plugin load/reload.\"": "), \": 保管库(Vault)索引在插件加载或重载时刷新。\"", "), \": Vault index is refreshed when switching to Vault QA mode.\"": "), \": 切换到保管库(Vault) QA 模式时刷新保管库(Vault)索引。\"", "), 'By \"refreshed\", it means the vault index is not rebuilt from scratch but rather updated incrementally with new/modified notes since the last index. If you need a complete rebuild, run the commands \"Clear vector store\" and \"Force re-index for QA\" manually. This helps reduce costs when using paid embedding models.'": "), '所谓的\"刷新\"意味着保管库(Vault)索引不会从头开始重建，而是通过增量方式更新自上次索引以来的新笔记或修改过的笔记。如果您需要完全重建索引，请手动运行命令\"清除向量数据库\"和\"强制重新索引以进行 QA\"。这有助于在使用付费嵌入模型时减少成本。'", "), \"Beware of the cost if you are using a paid embedding model and have a large vault! You can run Copilot command \"": "), \"如果您使用的是付费嵌入模型并且保管库(Vault)很大，请注意成本！您可以运行 Copilot 命令，\"", "), \" and refer to your selected embedding model pricing to estimate indexing costs.\"": "), \"并参考您所选嵌入模型的定价来估算索引成本。\"", "Error(\"Private accessor was defined without a getter\"": "Error(\"私有访问器定义时没有提供 getter\"", "Error(\"Cannot read private member from an object whose class did not declare it\"": "Error(\"无法从其类未声明的对象读取私有成员\"", "Error(\"Private method is not writable\"": "Error(\"私有方法不可写\"", "Error(\"Private accessor was defined without a setter\"": "Error(\"私有访问器定义时没有提供 setter\"", "Error(\"Cannot write private member to an object whose class did not declare it\"": "Error(\"无法向其类未声明的对象写入私有成员\"", "Error(`Can't use \"invalid_type_error\" or \"required_error\" in conjunction with custom error map.`": "Error(`不能在自定义错误映射中使用 \"invalid_type_error\" 或 \"required_error\"`", "Error(`Not a ZodError: ${value}`": "Error(`不是 ZodError: ${value}`", "Error(\"Validation failed but no issues detected.\"": "Error(\"验证失败但未检测到问题。\"", "Error(\"Synchronous parse encountered promise.\"": "Error(\"同步解析遇到 Promise。\"", "Error(`Internal ZodObject error: invalid unknownKeys value.`": "Error(`内部 ZodObject 错误:无效的 unknownKeys 值。`", "Error(`A discriminator value for key \\`": "Error(`带有键 \\ 的区分器值 \\`", "Error(`Discriminator property ${String(discriminator)} has duplicate value ${String(value)}`": "Error(`区分属性 ${String(discriminator)} 有重复值 ${String(value)}`", "Error(\"You must pass an array of schemas to z.tuple([ ... ])\"": "Error(\"必须将模式数组传递给 z.tuple([...])\"", "Error(\"Async refinement encountered during synchronous parse operation. Use .parseAsync instead.\"": "Error(\"异步细化在同步解析操作中遇到。使用 .parseAsync 替代。\"", "Error(`Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.`": "Error(`同步解析操作中遇到异步转换。使用 .parseAsync 替代。`", "Error(\"RetryOperation timeout occurred\"": "Error(\"重试操作超时\"", "Error(\"minTimeout is greater than maxTimeout\"": "Error(\"minTimeout 大于 maxTimeout\"", "Error(`Non-error was thrown: \"${error}\". You should only throw errors.`": "Error(`抛出了非错误:\"${error}\"。您应该只抛出错误。`", "Error(\"crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported\"": "Error(\"crypto.getRandomValues() 不受支持。参见 https://github.com/uuidjs/uuid#getrandomvalues-not-supported\"", "Error(\"Expected a string\"": "Error(\"期望是一个字符串\"", "Error(\"Expected the input to be `string | string[]`\"": "Error(\"期望输入为 `string | string[]`\"", "Error(\"No LLM run to end.\"": "Error(\"没有 LLM 运行结束。\"", "Error(\"No chain run to end.\"": "Error(\"没有LLM链运行结束。\"", "Error(\"No tool run to end\"": "Error(\"没有工具运行结束。\"", "Error(\"No retriever run to end\"": "Error(\"没有检索器运行结束。\"", "Error(`Invalid \"runId\" provided to \"handleLLMNewToken\" callback.`": "Error(`提供的 \"runId\" 无效。`", "Error(\"The listener must be a function\"": "Error(\"监听器必须是一个函数\"", "Error(\"Expected `milliseconds` to be a positive number\"": "Error(\"期望 `milliseconds` 是一个正数\"", "Error(`Expected \\`": "Error(`期望 \\`", "Error(`Invalid UUID: ${str2}`": "Error(`无效的 UUID: ${str2}`", "Error(`LANGCHAIN_TRACING_SAMPLING_RATE must be between 0 and 1 if set. Got: ${samplingRate}`": "Error(`LANGCHAIN_TRACING_SAMPLING_RATE 必须在 0 和 1 之间(如果设置了的话)。得到: ${samplingRate}`", "Error(`Failed to ${operation}: ${response.status} ${response.statusText} ${body}`": "Error(`${operation} 失败: ${response.status} ${response.statusText} ${body}`", "Error(\"Number of items to pop off may not be less than 1.\"": "Error(\"弹出项的数量不得少于 1。\"", "Error(`Failed to fetch ${path}: ${response.status} ${response.statusText}`": "Error(`获取 ${path} 失败: ${response.status} ${response.statusText}`", "Error(\"Failed to retrieve server info.\"": "Error(\"获取服务器信息失败。\"", "Error(`Run ${runId} has no app_path`": "Error(`运行 ${runId} 没有 app_path`", "Error(\"Must provide either runId or run\"": "Error(\"必须提供 runId 或 run\"", "Error(`Child run ${childRun.id} has no parent`": "Error(`子运行 ${childRun.id} 没有父运行`", "Error(\"Invalid response from server\"": "Error(\"无效的服务器响应\"", "Error(\"Either datasetId or datasetName must be given\"": "Error(\"必须提供 datasetId 或 datasetName\"", "Error(`Failed to create session ${projectName}: ${response.status} ${response.statusText}`": "Error(`创建会话 ${projectName} 失败: ${response.status} ${response.statusText}`", "Error(`Failed to update project ${projectId}: ${response.status} ${response.statusText}`": "Error(`更新项目 ${projectId} 失败: ${response.status} ${response.statusText}`", "Error(\"Must provide either projectName or projectId, not both\"": "Error(\"必须提供 projectName 或 projectId,但不能同时提供两者\"", "Error(\"Must provide projectName or projectId\"": "Error(\"必须提供 projectName 或 projectId\"", "Error(`Project[id=${projectId}, name=${projectName}] not found`": "Error(`项目[id=${projectId}, name=${projectName}] 未找到`", "Error(\"No projects found to resolve tenant.\"": "Error(\"未找到可解决租户的项目。\"", "Error(`Dataset ${fileName} already exists`": "Error(`数据集 ${fileName} 已存在`", "Error(`Failed to upload CSV: ${response.status} ${response.statusText}`": "Error(`上传 CSV 失败: ${response.status} ${response.statusText}`", "Error(`Dataset ${name2} already exists`": "Error(`数据集 ${name2} 已存在`", "Error(`Failed to create dataset ${response.status} ${response.statusText}`": "Error(`创建数据集失败: ${response.status} ${response.statusText}`", "Error(\"Must provide either datasetName or datasetId, not both\"": "Error(\"必须提供 datasetName 或 datasetId,但不能同时提供两者\"", "Error(\"Must provide datasetName or datasetId\"": "Error(\"必须提供 datasetName 或 datasetId\"", "Error(`Dataset[id=${datasetId}, name=${datasetName}] not found`": "Error(`数据集[id=${datasetId}, name=${datasetName}] 未找到`", "Error(\"Must provide either datasetName or datasetId\"": "Error(\"必须提供 datasetName 或 datasetId\"", "Error(`Failed to delete ${path}: ${response.status} ${response.statusText}`": "Error(`删除 ${path} 失败: ${response.status} ${response.statusText}`", "Error(`Failed to create example: ${response.status} ${response.statusText}`": "Error(`创建示例失败: ${response.status} ${response.statusText}`", "Error(`Failed to create examples: ${response.status} ${response.statusText}`": "Error(`创建示例失败: ${response.status} ${response.statusText}`", "Error(\"Must provide a datasetName or datasetId\"": "Error(\"必须提供数据集名称或数据集 ID\"", "Error(`Failed to update example ${exampleId}: ${response.status} ${response.statusText}`": "Error(`更新示例 ${exampleId} 失败: ${response.status} ${response.statusText}`", "Error(`Invalid run type: ${typeof run}`": "Error(`无效的运行类型: ${typeof run}`", "Error(\"One of runId or projectId must be provided\"": "Error(\"必须提供 runId 或 projectId\"", "Error(\"Only one of runId or projectId can be provided\"": "Error(\"只能提供 runId 或 projectId 中的一个\"", "Error(`field[${key}] already exists in the message chunk, but with a different type.`": "Error(`字段 [${key}] 在消息块中已存在,但类型不同。`", "Error(\"Malformed tool call chunk args.\"": "Error(\"工具调用块参数格式错误。\"", "Error(`Unable to coerce message from array: only human, AI, or system message coercion is currently supported.`": "Error(`无法从数组强制转换消息:目前仅支持 human, AI 或 system 消息的强制转换。`", "Error(`Got unsupported message type: ${m3._getType()}`": "Error(`收到不受支持的消息类型: ${m3._getType()}`", "Error(\"Unknown message type.\"": "Error(\"未知的消息类型。\"", "error(`Error in handler ${handler.constructor.name}, handleText: ${err}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleText: ${err}`", "error(`Error in handler ${handler.constructor.name}, handleRetriever`": "error(`处理器 ${handler.constructor.name} 中的错误,handleRetriever`", "error(`Error in handler ${handler.constructor.name}, handleRetrieverError: ${error}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleRetrieverError: ${error}`", "error(`Error in handler ${handler.constructor.name}, handleLLMNewToken: ${err}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleLLMNewToken: ${err}`", "error(`Error in handler ${handler.constructor.name}, handleLLMError: ${err2}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleLLMError: ${err2}`", "error(`Error in handler ${handler.constructor.name}, handleLLMEnd: ${err}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleLLMEnd: ${err}`", "error(`Error in handler ${handler.constructor.name}, handleChainError: ${err2}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleChainError: ${err2}`", "error(`Error in handler ${handler.constructor.name}, handleChainEnd: ${err}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleChainEnd: ${err}`", "error(`Error in handler ${handler.constructor.name}, handleAgentAction: ${err}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleAgentAction: ${err}`", "error(`Error in handler ${handler.constructor.name}, handleAgentEnd: ${err}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleAgentEnd: ${err}`", "error(`Error in handler ${handler.constructor.name}, handleToolError: ${err2}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleToolError: ${err2}`", "error(`Error in handler ${handler.constructor.name}, handleToolEnd: ${err}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleToolEnd: ${err}`", "error(`Error in handler ${handler.constructor.name}, handleLLMStart: ${err}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleLLMStart: ${err}`", "error(`Error in handler ${handler.constructor.name}, handleChainStart: ${err}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleChainStart: ${err}`", "error(`Error in handler ${handler.constructor.name}, handleToolStart: ${err}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleToolStart: ${err}`", "error(`Error in handler ${handler.constructor.name}, handleRetrieverStart: ${err}`": "error(`处理器 ${handler.constructor.name} 中的错误,handleRetrieverStart: ${err}`", "Error(\"Test operation failed\", \"TEST_OPERATION_FAILED\"": "Error(\"测试操作失败` , `TEST_OPERATION_FAILED\"", "Error(\"Operation `op` property is not one of operations defined in RFC-6902\", \"OPERATION_OP_INVALID\"": "Error(\"操作 'op' 属性不是 RFC-6902 中定义的操作之一` , `OPERATION_OP_INVALID\"", "Error(\"JSON-Patch: modifying `__proto__` or `constructor/prototype` prop is banned for security reasons, if this was on purpose, please set `banPrototypeModifications` flag false and pass it to this function. More info in fast-json-patch README\"": "Error(\"JSON-Patch:出于安全原因,禁止修改 ' __proto__ ' 或 ' constructor/prototype ' 属性,如果这是故意的,请将 ' banPrototypeModifications ' 标志设置为 false 并将其传递给此函数。更多信息参见 fast-json-patch README\"", "Error(\"Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index\", \"OPERATION_PATH_ILLEGAL_ARRAY_INDEX\"": "Error(\"期望一个无符号十进制整数值,使新引用的值为具有零基索引的数组元素` , `OPERATION_PATH_ILLEGAL_ARRAY_INDEX\"", "Error(\"The specified index MUST NOT be greater than the number of elements in the array\", \"OPERATION_VALUE_OUT_OF_BOUNDS\"": "Error(\"指定的索引不得大于数组中的元素数量` , `OPERATION_VALUE_OUT_OF_BOUNDS\"", "Error(\"Cannot perform operation at the desired path\", \"OPERATION_PATH_UNRESOLVABLE\"": "Error(\"无法在所需路径执行操作` , `OPERATION_PATH_UNRESOLVABLE\"", "Error(\"Patch sequence must be an array\", \"SEQUENCE_NOT_AN_ARRAY\"": "Error(\"补丁序列必须是一个数组` , `SEQUENCE_NOT_AN_ARRAY\"", "Error(\"Operation is not an object\", \"OPERATION_NOT_AN_OBJECT\"": "Error(\"操作不是一个对象` , `OPERATION_NOT_AN_OBJECT\"", "Error(\"Operation `path` property is not a string\", \"OPERATION_PATH_INVALID\"": "Error(\"操作 'path' 属性不是一个字符串` , `OPERATION_PATH_INVALID\"", "Error('Operation `path` property must start with \"/\"'": "Error('操作 `path` 属性必须以 \"/\" 开头'", "Error(\"Operation `from` property is not present (applicable in `move` and `copy` operations)\", \"OPERATION_FROM_REQUIRED\"": "Error(\"操作 'from' 属性不存在(适用于 'move' 和 'copy' 操作)` , `OPERATION_FROM_REQUIRED\"", "Error(\"Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)\", \"OPERATION_VALUE_REQUIRED\"": "Error(\"操作 'value' 属性不存在(适用于 'add', 'replace' 和 'test' 操作)` , `OPERATION_VALUE_REQUIRED\"", "Error(\"Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)\", \"OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED\"": "Error(\"操作 'value' 属性不存在(适用于 'add', 'replace' 和 'test' 操作)` , `OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED\"", "Error(\"Cannot perform an `add` operation at the desired path\", \"OPERATION_PATH_CANNOT_ADD\"": "Error(\"无法在所需路径执行 'add' 操作` , `OPERATION_PATH_CANNOT_ADD\"", "Error(\"Cannot perform the operation at a path that does not exist\", \"OPERATION_PATH_UNRESOLVABLE\"": "Error(\"无法在不存在的路径执行操作` , `OPERATION_PATH_UNRESOLVABLE\"", "Error(\"Cannot perform the operation from a path that does not exist\", \"OPERATION_FROM_UNRESOLVABLE\"": "Error(\"无法从不存在的路径执行操作` , `OPERATION_FROM_UNRESOLVABLE\"", "null, \"Default\"": "null, \"默认\"", "null, \"Model\"": "null, \"模型\"", "null, \"Provider\"": "null, \"提供商\"", "null, \"Enabled\"": "null, \"启用\"", "null, \"Delete\"": "null, \"删除\"", "null, \"Advanced Settings\"": "null, \"高级设置\"", "null, \"API Settings\"": "null, \"API 设置\"", "null, \"All your API keys are stored locally.\"": "null, \"所有 API 密钥都存储在本地。\"", "null, \"You can find your API key at\", \" \"": "null, \"您可以在 \", \" 找到您的 API 密钥。\"", "null, \"If you are a new user, try \"": "null, \"如果您是新用户,请尝试 \"", "null, \" to see if you have correct API access first.\"": "null, \"以确认您是否有正确的 API 访问权限。\"", "null, \"If you have Google Cloud, you can get Gemini API key\", \" \"": "null, \"如果您有 Google Cloud,您可以获取 Gemini API 密钥 \", \"\"", "null, \"If you have Anthropic API access, you can get the API key\", \" \"": "null, \"如果您有 Anthropic API 访问权限,您可以获取 API 密钥 \", \"\"", "null, \"You can get your OpenRouterAI key\", \" \"": "null, \"您可以获取您的 OpenRouterAI 密钥 \", \"。\"", "null, \"If you have Groq API access, you can get the API key\", \" \"": "null, \"如果您有 Groq API 访问权限,您可以获取 API 密钥 \", \"\"", "null, \"Get your free Cohere API key\", \" \"": "null, \"获取您的免费 Cohere API 密钥 \", \"\"", "null, \"General Settings\"": "null, \"通用设置\"", "null, \"Please be mindful of the number of tokens and context conversation turns you set here, as they will affect the cost of your API requests.\"": "null, \"请注意您需要在这里设置最大输出token数量和上下文对话轮数,因为它们会影响您的 API 的请求成本。\"", "null, \"The maximum number of \"": "null, \"最大输出\"", "null, \"output tokens\"": "null, \"Token数\"", "null, \"This number plus the length of your prompt (input tokens) must be smaller than the context window of the model.\"": "null, \"此数字加上您聊天提示词的长度(输入Token)必须小于模型的上下文窗口。\"", "null, \"QA Settings\"": "null, \"QA 设置\"", "null, \"QA mode relies a \"": "null, \"QA 模式依赖于一个\"", "null, \"Long Note QA vs. Vault QA (BETA)\"": "null, \"长笔记 QA 与 保管库(Vault) QA(测试版)\"", "null, \"Long Note QA mode uses the Active Note as context. Vault QA (BETA) uses your entire vault as context. Please ask questions as specific as possible, avoid vague questions to get better results.\"": "null, \"长笔记 QA 模式使用活动笔记作为上下文。保管库(Vault) QA(测试版)使用您的整个保管库(Vault)作为上下文。请尽可能具体地提问,避免模糊的问题以获得更好的结果。\"", "null, \"Local Embedding Model\"": "null, \"本地嵌入模型\"", "null, \"Check the\", \" \"": "null, \"检查 \", \"\"", "null, \"Embedding Models\"": "null, \"嵌入模型\"", "null, \"Auto-Index Strategy\"": "null, \"自动索引策略\"", "null, \"When you switch to \"": "null, \"当您切换到\"", "null, \"Long Note QA\"": "null, \"长笔记 QA\"", "null, \"Vault QA\"": "null, \"保管库(Vault) QA\"", "null, \"based on the auto-index strategy you select below\"": "null, \"基于您选择的自动索引策略\"", "null, \"Index vault for QA\"": "null, \"为 QA模式 显式索引保管库(Vault)\"", "null, \"Refresh Index\"": "null, \"刷新索引\"", "null, \"ON STARTUP\"": "null, \"启动时\"", "null, \"ON MODE SWITCH (Recommended)\"": "null, \"模式切换时(推荐)\"", "null, \"Count total tokens in your vault\"": "null, \"计算您保管库(Vault)中的总Token数\"", "null, \"Copilot Settings\"": "null, \"Copilot 设置\"", "}, \"Model Selection\"": "}, \"模型选择\"", "}, \"New Chat\"": "}, \"新建聊天\"", "}, \"Save as Note\"": "}, \"保存为笔记\"", "}, \"Chat\"": "}, \"聊天\"", "}, \"Long Note QA\"": "}, \"长笔记 QA\"", "}, \"Vault QA (BETA)\"": "}, \"保管库(Vault) QA(测试版)\"", "}, \"Mode Selection\"": "}, \"模式选择\"", "}, \"Send Note(s) to Prompt\"": "}, \"将笔记发送到聊天提示词\"", "}, \"Refresh Index\"": "}, \"刷新索引\"", "}, \"Delete\"": "}, \"删除\"", "}, \"Add Custom Model \"": "}, \"添加自定义模型\"", "}, \"Add Model\"": "}, \"添加模型\"", "}, \"Make sure you have access to the model and the correct API key.\"": "}, \"请确保您有访问该模型的权限并且拥有正确的 API 密钥。\"", "}, \"Command Settings \"": "}, \"命令设置\"", "}, \"Vault QA is in BETA and may not be stable. If you have issues please report in the github repo.\"": "}, \"保管库(Vault) QA 处于测试版,可能不稳定。如有问题,请在 GitHub 仓库中报告。\"", "}, \"local copilot\"": "}, \"本地 Copilot\"", "}, \"If you are using a paid embedding provider, beware of costs for large vaults!\"": "}, \"如果您使用的是付费嵌入模型提供商,请注意大型保管库(Vault)的成本！\"", "}, \"Save and Reload\"": "}, \"保存并重载\"", "}, \"Reset to Default Settings\"": "}, \"重置为默认设置\"", "}, \"Please Save and Reload the plugin when you change any setting below!\"": "}, \"更改任何设置后,请保存并重载插件！\"", "description: \"Default is 3 (Recommended). Increase if you want more sources from your notes. A higher number can lead to irrelevant sources and lower quality responses, it also fills up the context window faster.\"": "description: \"默认是 3(推荐)。如果你想从笔记中获得更多来源可以增加这个数值。较高的数值可能导致无关来源和较低质量的回答,同时也会更快地填满上下文窗口。\"", "console.error(`Error in handler ${handler.constructor.name}, handleText: ${err}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleText: ${err}`", "console.error(`Error in handler ${handler.constructor.name}, handleRetriever`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleRetriever`", "console.error(`Error in handler ${handler.constructor.name}, handleRetrieverError: ${error}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleRetrieverError: ${error}`", "console.error(`Error in handler ${handler.constructor.name}, handleLLMNewToken: ${err}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleLLMNewToken: ${err}`", "console.error(`Error in handler ${handler.constructor.name}, handleLLMError: ${err2}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleLLMError: ${err2}`", "console.error(`Error in handler ${handler.constructor.name}, handleLLMEnd: ${err}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleLLMEnd: ${err}`", "console.error(`Error in handler ${handler.constructor.name}, handleChainError: ${err2}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleChainError: ${err2}`", "console.error(`Error in handler ${handler.constructor.name}, handleChainEnd: ${err}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleChainEnd: ${err}`", "console.error(`Error in handler ${handler.constructor.name}, handleAgentAction: ${err}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleAgentAction: ${err}`", "console.error(`Error in handler ${handler.constructor.name}, handleAgentEnd: ${err}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleAgentEnd: ${err}`", "console.error(`Error in handler ${handler.constructor.name}, handleToolError: ${err2}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleToolError: ${err2}`", "console.error(`Error in handler ${handler.constructor.name}, handleToolEnd: ${err}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleToolEnd: ${err}`", "console.error(`Error in handler ${handler.constructor.name}, handleLLMStart: ${err}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleLLMStart: ${err}`", "console.error(`Error in handler ${handler.constructor.name}, handleChainStart: ${err}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleChainStart: ${err}`", "console.error(`Error in handler ${handler.constructor.name}, handleToolStart: ${err}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleToolStart: ${err}`", "console.error(`Error in handler ${handler.constructor.name}, handleRetrieverStart: ${err}`": "console.error(`处理器 ${handler.constructor.name} 中的错误,handleRetrieverStart: ${err}`", "console.error(`Unable to parse ${JSON.stringify(value)} as a whatwg URL.`": "console.error(`无法将 ${JSON.stringify(value)} 解析为 whatwg URL。`", "console.error(`Failed to construct URL with ${expression}`": "console.error(`使用 ${expression} 构建 URL 失败`", "console.error(`Could not parse message into JSON:`": "console.error(`无法将消息解析为 JSON:`", "console.error(`From chunk:`": "console.error(`来自块:`", "console.error(\"Error parsing YAML frontmatter:\"": "console.error(\"解析 YAML 前置事项时出错:\"", "console.error(\"Error parsing function arguments\"": "console.error(\"解析函数参数时出错\"", "console.error(`No embedding model found for: ${embeddingModelKey}`": "console.error(`找不到嵌入模型: ${embeddingModelKey}`", "console.error(`API key is not provided for the embedding model: ${embeddingModelKey}`": "console.error(`未为嵌入模型提供 API 密钥: ${embeddingModelKey}`", "console.error(`Error creating embedding model: ${embeddingModelKey}`": "console.error(`创建嵌入模型时出错: ${embeddingModelKey}`", "console.error(`No embedding model found for key: ${modelKey}`": "console.error(`找不到键对应的嵌入模型: ${modelKey}`", "console.error(\"EmbeddingManager could not determine model name!\"": "console.error(\"EmbeddingManager 无法确定模型名称！\"", "console.error(\"Error storing vectors in VectorDB:\"": "console.error(\"在 VectorDB 中存储向量时出错:\"", "console.error(\"Error getting note files from VectorDB:\"": "console.error(\"从 VectorDB 获取笔记文件时出错:\"", "console.error(\"Error removing file from VectorDB:\"": "console.error(\"从 VectorDB 删除文件时出错:\"", "console.error(\"Error getting newest file mtime from VectorDB:\"": "console.error(\"从 VectorDB 获取最新文件 mtime 时出错:\"", "console.error(\"Error checking last embedding model from VectorDB:\"": "console.error(\"从 VectorDB 检查最后一个嵌入模型时出错:\"", "console.error(`An error occurred while creating dataset ${finalDatasetName}. You should delete it manually.`": "console.error(`创建数据集 ${finalDatasetName} 时出错。你应该手动删除它。`", "console.error(\"Resetting default model. No model configuration found for: \"": "console.error(\"重置默认模型。未找到模型配置: \"", "console.error(\"createChainWithNewModel failed: \"": "console.error(\"createChainWithNewModel 失败: \"", "console.error(\"Error creating chain:\"": "console.error(\"创建LLM链时出错:\"", "console.error(\"setChain failed: No chat model set.\"": "console.error(\"设置LLM链时出错:没有设置聊天模型。\"", "console.error(\"Error getting embeddings API. Please check your settings.\"": "console.error(\"获取嵌入 API 时出错。请检查您的设置。\"", "console.error(\"Error creating vector store.\"": "console.error(\"创建向量存储时出错。\"", "console.error(\"Chain type not supported:\"": "console.error(\"不支持的LLM链类型:\"", "console.error(\"App instance is not available.\"": "console.error(\"应用程序实例不可用。\"", "console.error(\"No active note found.\"": "console.error(\"未找到活动笔记。\"", "console.error(\"Model request failed:\"": "console.error(\"模型请求失败:\"", "console.error(\"Error saving chat as note:\"": "console.error(\"保存聊天为笔记时出错:\"", "console.error(\"No note content found.\"": "console.error(\"未找到笔记内容。\"", "console.error(\"Error regenerating message:\"": "console.error(\"重新生成消息时出错:\"", "console.error(\"Error reloading plugin:\"": "console.error(\"重新加载插件时出错:\"", "console.error(\"Error clearing the local vector store:\"": "console.error(\"清除本地向量存储时出错:\"", "console.error(\"Error indexing vault to vector store:\"": "console.error(\"将保管库(Vault)索引到向量存储时出错:\"", "console.error(\"Error re-indexing vault to vector store:\"": "console.error(\"重新索引保管库(Vault)到向量存储时出错:\"", "console.error(\"Error saving vault to vector store:\"": "console.error(\"保存保管库(Vault)到向量存储时出错:\"", "console.error(\"Error dumping custom prompts:\"": "console.error(\"导出自定义聊天提示词时出错:\"", "console.error(\"Error clearing vector store for reindexing:\"": "console.error(\"重新索引时清除向量存储时出错:\"", "console.error(\"Error indexing file:\"": "console.error(\"索引文件时出错:\"", "console.error(\"Error counting tokens: \"": "console.error(\"计数Token时出错:\"", "console.log(\"Using RetryOperation.try() is deprecated\")": "console.log(\"使用 RetryOperation.try() 已废弃\")", "console.log(\"Using RetryOperation.start() is deprecated\")": "console.log(\"使用 RetryOperation.start() 已废弃\")", "console.log(\"New LLM chain created.\")": "console.log(\"新的 LLM 链已创建。\")", "console.log(\"[WARNING]: Received non-string content from OpenAI. This is currently not supported.\")": "console.log(\"[警告]: 收到来自 OpenAI 的非字符串内容。目前不支持。\")", "console.log(\"Failed to parse error response as JSON\")": "console.log(\"未能将错误响应解析为 JSON\")", "console.log(\"Getting text from response\")": "console.log(\"从响应中获取文本\")", "console.log(\"Failed to get text from error response\")": "console.log(\"未能从错误响应中获取文本\")", "console.log(\"clearing chat memory\")": "console.log(\"清除聊天记录\")", "console.log(\"User stopping generation...\")": "console.log(\"用户停止生成...\")", "console.log(\"Message regenerated successfully\")": "console.log(\"消息重新生成成功\")", "console.log(\"Local vector store cleared successfully, new instance created.\")": "console.log(\"本地向量存储清除成功，创建了新实例。\")", "console.log(\"Local vector store garbage collected successfully, new instance created.\")": "console.log(\"本地向量存储垃圾回收成功，创建了新实例。\")", "console.log(\"Detected change in embedding model. Rebuild vector store from scratch.\")": "console.log(\"检测到嵌入模型的变化。从头重建向量存储。\")", "console.log(\"Indexing Errors:\", errors.join(\"\\n\")": "console.log(\"索引错误：\", errors.join(\"\\n\")", "title: \"OpenAI API Key\"": "title: \"OpenAI API 密钥\"", "title: \"OpenAI Organization ID (optional)\"": "title: \"OpenAI 组织 ID(可选)\"", "title: \"Google API Settings\"": "title: \"Google API 设置\"", "title: \"Google API Key\"": "title: \"Google API 密钥\"", "title: \"Anthropic API Settings\"": "title: \"Anthropic API 设置\"", "title: \"Anthropic API Key\"": "title: \"Anthropic API 密钥\"", "title: \"OpenRouter.ai API Settings\"": "title: \"OpenRouter.ai API 设置\"", "title: \"OpenRouter AI API Key\"": "title: \"OpenRouter AI API 密钥\"", "title: \"Azure OpenAI API Settings\"": "title: \"Azure OpenAI API 设置\"", "title: \"Azure OpenAI API Key\"": "title: \"Azure OpenAI API 密钥\"", "title: \"Azure OpenAI API Instance Name\"": "title: \"Azure OpenAI API 实例名称\"", "title: \"Azure OpenAI API Deployment Name\"": "title: \"Azure OpenAI API 部署名称\"", "title: \"Azure OpenAI API Version\"": "title: \"Azure OpenAI API 版本\"", "title: \"Azure OpenAI API Embedding Deployment Name\"": "title: \"Azure OpenAI API 嵌入部署名称\"", "title: \"Groq API Settings\"": "title: \"Groq API 设置\"", "title: \"Groq API Key\"": "title: \"Groq API 密钥\"", "title: \"Cohere API Settings\"": "title: \"Cohere API 设置\"", "title: \"Cohere API Key\"": "title: \"Cohere API 密钥\"", "placeholder: \"Ask anything. [[ for notes. / for custom prompts.\"": "placeholder: \"询问任何问题。[[用于笔记。/ 用于自定义聊天提示词。\"", "placeholder: \"Enter /folderPath, [[note title]] separated by commas\"": "placeholder: \"输入 /folder<PERSON>ath,[[笔记标题]] 用逗号分隔\"", "placeholder: \"Enter model name\"": "placeholder: \"输入模型名称\"", "placeholder: \"Enter API key\"": "placeholder: \"输入 API 密钥\"", "placeholder: \"Enter OpenAI API Key\"": "placeholder: \"输入 OpenAI API 密钥\"", "placeholder: \"Enter OpenAI Organization ID if applicable\"": "placeholder: \"如果适用,请输入 OpenAI 组织 ID\"", "placeholder: \"Enter Google API Key\"": "placeholder: \"输入 Google API 密钥\"", "placeholder: \"Enter Anthropic API Key\"": "placeholder: \"输入 Anthropic API 密钥\"", "placeholder: \"Enter OpenRouter AI API Key\"": "placeholder: \"输入 OpenRouter AI API 密钥\"", "placeholder: \"Enter Azure OpenAI API Key\"": "placeholder: \"输入 Azure OpenAI API 密钥\"", "placeholder: \"Enter Azure OpenAI API Instance Name\"": "placeholder: \"输入 Azure OpenAI API 实例名称\"", "placeholder: \"Enter Azure OpenAI API Deployment Name\"": "placeholder: \"输入 Azure OpenAI API 部署名称\"", "placeholder: \"Enter Azure OpenAI API Version\"": "placeholder: \"输入 Azure OpenAI API 版本\"", "placeholder: \"Enter Azure OpenAI API Embedding Deployment Name\"": "placeholder: \"输入 Azure OpenAI API 嵌入部署名称\"", "placeholder: \"Enter Groq API Key\"": "placeholder: \"输入 Groq API 密钥\"", "placeholder: \"Enter Cohere API Key\"": "placeholder: \"输入 Cohere API 密钥\"", "placeholderText = \"Please enter your custom ad-hoc prompt here, press enter to send.\"": "placeholderText = \"请在这里输入您的自定义即时聊天提示词,按回车发送。\"", "Notice(`Setting model: ${modelConfig.modelName}`": "Notice(`设置模型: ${modelConfig.modelName}`", "Notice(`Error creating model: ${modelKey}`": "Notice(`创建模型错误: ${modelKey}`", "Notice(\"Error creating chain:\"": "Notice(\"创建LLM链错误:\"", "Notice(\"No note content provided\"": "Notice(\"没有提供笔记内容\"", "Notice(`LangChain error: ${errorCode}`": "Notice(`LangChain 错误: ${errorCode}`", "Notice(\"Please select a language.\"": "Notice(\"请选择一种语言。\"", "Notice(\"Please select a tone.\"": "Notice(\"请选择一种语气。\"", "Notice(`Total tokens in your vault: ${totalTokens}`": "Notice(`您的保管库(Vault)中的总Token数: ${totalTokens}`", "Notice(\"Please fill in both fields: Title and Prompt.\"": "Notice(\"请填写两个字段:标题和聊天提示词。\"", "Notice(\"No active note found.\"": "Notice(\"未找到活动笔记。\"", "Notice(`Warning: No valid notes found for the provided path '${variableName}'.`": "Notice(`警告:对于提供的路径 '${variableName}' 没有找到有效的笔记。`", "Notice(\"Model request failed:\"": "Notice(\"模型请求失败:\"", "Notice(`Chat saved as note in folder: ${defaultSaveFolder}.`": "Notice(`聊天已保存为文件夹中的笔记: ${defaultSaveFolder}。`", "Notice(\"No valid Chat context provided. Defaulting to the active note.\"": "Notice(\"没有提供有效的聊天上下文。默认使用活动笔记。\"", "Notice(\"No note content found.\"": "Notice(\"未找到笔记内容。\"", "Notice(\"Vault index refreshed.\"": "Notice(\"保管库(Vault)索引已刷新。\"", "Notice(\"Cannot regenerate the first message or a user message.\"": "Notice(\"无法重新生成第一条消息或用户消息。\"", "Notice(\"Failed to regenerate message. Please try again.\"": "Notice(\"重新生成消息失败。请再次尝试。\"", "Notice(\"No active leaf found.\"": "Notice(\"未找到活动节点。\"", "Notice(\"Failed to open a markdown view.\"": "Notice(\"打开 Markdown 视图失败。\"", "Notice(\"Message inserted into the active note.\"": "Notice(\"消息插入到了活动笔记中。\"", "Notice(\"Please fill in necessary fields!\"": "Notice(\"请填写必要的字段！\"", "Notice(\"Plugin reloaded successfully.\"": "Notice(\"插件成功重新加载。\"", "Notice(\"Failed to reload the plugin. Please reload manually.\"": "Notice(\"重新加载插件失败。请手动重新加载。\"", "Notice(\"Custom prompt saved successfully.\"": "Notice(\"自定义聊天提示词保存成功。\"", "Notice(\"Error saving custom prompt. Please check if the title already exists.\"": "Notice(\"保存自定义聊天提示词错误。请检查标题是否已存在。\"", "Notice(\"Please select a prompt title.\"": "Notice(\"请选择一个聊天提示词标题。\"", "Notice(`No prompt found with the title \"${promptTitle}\".`": "Notice(`未找到标题为 ‘${promptTitle}’ 的聊天提示词。`", "Notice(\"An error occurred.\"": "Notice(\"发生了一个错误。\"", "Notice(`Prompt \"${promptTitle}\" has been deleted.`": "Notice(`聊天提示词 ‘${promptTitle}’ 已被删除。`", "Notice(\"An error occurred while deleting the prompt.\"": "Notice(\"删除聊天提示词时发生错误。\"", "Notice(`Prompt \"${title}\" has been updated.`": "Notice(`聊天提示词 ‘${title}’ 已更新。`", "Notice(\"Local vector store cleared successfully.\"": "Notice(\"本地向量存储清除成功。\"", "Notice(\"An error occurred while clearing the local vector store.\"": "Notice(\"清除本地向量存储时发生错误。\"", "Notice(\"Local vector store garbage collected successfully.\"": "Notice(\"本地向量存储垃圾回收成功。\"", "Notice(`${indexedFileCount} vault files indexed to vector store.`": "Notice(`${indexedFileCount} 个保管库(Vault)文件已索引到向量存储。`", "Notice(\"An error occurred while indexing vault to vector store.\"": "Notice(\"将保管库(Vault)索引到向量存储时发生错误。\"", "Notice(\"An error occurred while re-indexing vault to vector store.\"": "Notice(\"重新索引保管库(Vault)到向量存储时发生错误。\"", "Notice(\"An error occurred while saving vault to vector store.\"": "Notice(\"保存保管库(Vault)到向量存储时发生错误。\"", "Notice(`Custom prompts dumped to ${folder} folder`": "Notice(`自定义聊天提示词已导出到 ${folder} 文件夹`", "Notice(\"Error dumping custom prompts. Check console for details.\"": "Notice(\"导出自定义聊天提示词时发生错误。请检查控制台了解详细信息。\"", "Notice(\"Embedding instance not found.\"": "Notice(\"未找到嵌入实例。\"", "Notice(\"Detected change in embedding model. Rebuild vector store from scratch.\"": "Notice(\"检测到嵌入模型发生变化。从头重建向量存储。\"", "Notice(\"Error clearing vector store for reindexing.\"": "Notice(\"重新索引时清除向量存储错误。\"", "Notice(\"Copilot vault index is up-to-date.\"": "Notice(\"Copilot 保管库(Vault)索引是最新的。\"", "Notice(`Indexing completed with errors. Check the console for details.`": "Notice(`索引完成但有错误。请检查控制台了解详细信息。`", "Notice(\"No chat history found.\"": "Notice(\"未找到聊天记录。\"", "appendText(\"Tip: turn on debug mode to show the processed prompt in the chat window.\"": "appendText(\"Tip：打开调试模式以在聊天窗口中显示处理后的聊天提示词。\"", "appendText(\"Check out the \"": "appendText(\"查看 \"", "appendText(\" for inspiration.\"": "appendText(\"以获得灵感。\"", "appendText(\"All notes under the path will be sent to the prompt when the \"": "appendText(\"在聊天模式下,当点击 \"", "appendText(\" button is clicked in Chat mode. \"": "appendText(\"按钮时,路径下的所有笔记都将被发送到聊天提示词。\"", "appendText(\"If none provided, \"": "appendText(\"如果没有提供,则\"", "appendText(\"All notes under the paths will be excluded from indexing\"": "appendText(\"路径下的所有笔记都将从索引中排除。\"", "appendText(\"Enable encryption for the API keys.\"": "appendText(\"启用 API 密钥的加密。\"", "appendText(\"Debug mode will log all API requests and prompts to the console.\"": "appendText(\"调试模式会将所有 API 请求和聊天提示词记录到控制台。\"", "text: \"Filter by Folder Path\"": "text: \"按文件夹路径筛选\"", "text: \"Send Note(s) to Prompt\"": "text: \"将笔记发送到聊天提示词\"", "text: \"default context is the active note\"": "text: \"默认上下文是活动笔记\"", "text: \"Filter by Tags\"": "text: \"按标签筛选\"", "text: \"Only tags in note property are used, tags in note content are not used.\"": "text: \"仅使用笔记属性中的标签,不使用笔记内容中的标签。\"", "text: \"All notes under the path above are further filtered by the specified tags. If no path is provided, only tags are used. Multiple tags should be separated by commas. \"": "text: \"路径上的所有笔记将进一步通过指定的标签进行筛选。如果没有提供路径,则仅使用标签。多个标签应以逗号分隔。\"", "text: \"Tags function as an OR filter, \"": "text: \"标签作为OR过滤器,\"", "any note that matches one of the tags will be sent to the prompt when button is clicked in Chat mode": "在聊天模式下,当点击按钮时,任何匹配其中一个标签的笔记都将被发送到聊天提示词", "text: \"Submit\"": "text: \"提交\"", "text: \"Exclude by Folder Path or Note Title\"": "text: \"按文件夹路径或笔记标题排除\"", "msg = `The value of \"${str2}\" is out of range.`": "msg = `值 '${str2}' 超出了范围。`", "msg = \"Invalid space after '<'.\"": "msg = \"后面的空格无效'<'.\"", "msg = \"Tag '\"": "msg = \"标签 '\"", "Msg = \"Chat model is not initialized properly, check your API key in Copilot setting and make sure you have API access.\"": "Msg = \"聊天模型未正确初始化,请检查 Copilot 设置中的 API 密钥并确保您有 API 访问权限。\"", "Msg = \"You do not have access to this model or the model does not exist, please check with your API provider.\"": "Msg = \"您没有访问该模型的权限或者该模型不存在,请与您的 API 提供商核实。\"", "Msg = \"Failed to load file, embedding API is not set correctly, please check your settings.\"": "Msg = \"文件加载失败,嵌入式 API 设置不正确,请检查您的设置。\"", "msg = \"Filter function threw: \"": "msg = \"过滤函数抛出异常:\"", "msg = \"Failed to open indexedDB, are you in private browsing mode?\"": "msg = \"无法打开 indexedDB,您是否处于隐私浏览模式?\"", "return `${name2} is outside of buffer bounds`": "return `${name2} 超出了缓冲区范围`", "return \"Attempt to access memory outside buffer bounds\"": "return \"尝试访问缓冲区范围外的内存\"", "return `The \"${name2}\" argument must be of type number. Received type ${typeof actual}`": "return `'${name2}' 参数必须是数字类型。接收到的类型为 ${typeof actual}`", "return `Expected ${input} to be returned from the \"${name2}\" function but got ${type}.`": "return `期望从 '${name2}' 函数返回 ${input} 但接收到的是 ${type}。`", "return `${msg} must be specified`": "return `${msg} 必须指定`", "return `The value of \"${str2}\" is out of range. It must be ${range}. Received ${received}`": "return `值 '${str2}' 超出了范围。它必须是 ${range}。接收到的是 ${received}`", "return `Basic ${token}`": "return `基本 ${token}`", "return `Bearer ${token}`": "return `认证 ${token}`", "return `Expected ${expectedType}. Received ${getTypeAsString(value)}.`": "return `期望 ${expectedType}。接收到的是 ${getTypeAsString(value)}。`", "return `Bearer ${bearer}`": "return `认证 ${bearer}`", "return \"Default\"": "return \"默认\"", "return \"clipboardData\"": "return \"剪贴板数据\"", "return `Please read the notes below and be ready to answer questions about them. `": "return `请阅读下面的笔记,并准备好回答有关这些笔记的问题。`", "return `Please follow the instructions closely step by step and rewrite the content to a thread. 1. Each paragraph must be under 240 characters. 2. The starting line is \\`": "return `请按照以下步骤仔细操作,并将内容重写成线程。1. 每段不得超过 240 个字符。2. 开始行是 \\`", "return `${status} status code (no body)`": "return `${status} 状态码(无正文)`", "return \"(no status code or body)\"": "return \"(无状态码或正文)\"", "return `other:${arch}`": "return `其他:${arch}`", "return `Other:${platform}`": "return `其他:${platform}`", "return \"Copilot Chat\"": "return \"Copilot 聊天\"", "return \"Copilot failed to decrypt API keys!\"": "return \"Copilot 解密 API 密钥失败！\"", "name: `${continuous ? \"continuous \" : \"\"}replication from ${info.db_name}`": "name: `${continuous ? \"continuous \" : \"\"}从 ${info.db_name} 复制`", "name: \"Set note context for Chat mode\"": "name: \"为聊天模式设置笔记上下文\"", "name: \"Set exclusion for Vault QA mode\"": "name: \"为保管库(Vault) QA 模式设置排除项\"", "name: \"Dump custom prompts to markdown files\"": "name: \"将自定义聊天提示词导出为 Markdown 文件\"", "message: `OK Feel free to ask me questions about [[${noteName}]].": "message: `好的，请随时就[[${noteName}]]向我提问。", "Please note that this is a retrieval-based QA for notes longer than the model context window. Specific questions are encouraged. For generic questions like 'give me a summary', 'brainstorm based on the content', Chat mode with *Send Note to Prompt* button used with a *long context model* is a more suitable choice.": "请注意，这是针对超过模型上下文窗口长度的笔记的检索式问答。鼓励提出具体问题。对于“给我一个总结”、“基于内容进行头脑风暴”等一般性问题，使用带有*发送笔记至聊天提示词*按钮和*长上下文模型*的聊天模式是更合适的选择。", "OK I've read these notes. Feel free to ask related questions, such as 'give me a summary of these notes in bullet points', 'what key questions does these notes answer', etc. ": "好的，我已经阅读了这些笔记。请随时提出相关问题，例如‘用项目符号总结这些笔记’、‘这些笔记回答了哪些关键问题’等。", "Please read the notes below and be ready to answer questions about them. If there's no information about a certain topic, just say the note does not mention it. The content of the note is between": "请阅读以下笔记，并准备好回答有关这些问题。如果没有关于某个主题的信息，就说笔记中没有提到它。笔记的内容在之间。", "Please fix the grammar and spelling of the following text and return it without any other changes:": "请修正以下文本的语法和拼写错误并原封不动地返回:", "Summarize the following text into bullet points and return it without any other changes. Identify the input language, and return the summary in the same language. If the input is English, return the summary in English. Otherwise, return in the same language as the input. Return ONLY the summary, DO NOT return the name of the language:": "将以下文本总结成要点并原封不动地返回。识别输入语言，并用相同语言返回总结。如果输入是英语，用英语返回总结。否则，用与输入相同的语言返回。只返回总结，不要返回语言名称:", "Please generate a table of contents for the following text and return it without any other changes. Output in the same language as the source, do not output English if it is not English:": "请为以下文本生成目录并原封不动地返回。用与源文本相同的语言输出，如果不是英语则不要输出英语:", "Please generate a glossary for the following text and return it without any other changes. Output in the same language as the source, do not output English if it is not English:": "请为以下文本生成词汇表并原封不动地返回。用与源文本相同的语言输出，如果不是英语则不要输出英语:", "Please simplify the following text so that a 6th-grader can understand. Output in the same language as the source, do not output English if it is not English:": "请简化以下文本以便六年级学生能理解。用与源文本相同的语言输出，如果不是英语则不要输出英语:", "Please insert emojis to the following content without changing the text.Insert at as many places as possible, but don't have any 2 emojis together. The original text must be returned.": "请在不改变文本的情况下，在以下内容中尽可能多地插入表情符号，但不要让两个表情符号连在一起。必须返回原始文本。", "Please remove all URLs from the following text and return it without any other changes:": "请移除以下文本中的所有网址并原封不动地返回:", "Please rewrite the following content to under 280 characters using simple sentences. Output in the same language as the source, do not output English if it is not English. Please follow the instruction strictly. Content:": "请用简单的句子重写以下内容，使其不超过280个字符。用与源文本相同的语言输出，如果不是英语则不要输出英语。请严格遵循指示。内容:", ", and the ending line is": ", 并且结束行是", "3. You must use": "3. 您必须使用", "to separate each paragraph! Then return it without any other changes. 4. Make it as engaging as possible.5. Output in the same language as the source, do not output English if it is not English.": "来分隔每个段落！然后原封不动地返回。4. 尽可能使它具有吸引力。5. 用与源文本相同的语言输出，如果不是英语则不要输出英语。", "The original content:": "原始内容:", "Please rewrite the following text to make it half as long while keeping the meaning as much as possible. Output in the same language as the source, do not output English if it is not English:": "请重写以下文本，使其长度减半，同时尽可能保留意义。用与源文本相同的语言输出，如果不是英语则不要输出英语:", "Please rewrite the following text to make it twice as long while keeping the meaning as much as possible. Output in the same language as the source, do not output English if it is not English:": "请重写以下文本，使其长度增加一倍，同时尽可能保留意义。用与源文本相同的语言输出，如果不是英语则不要输出英语:", "Please explain the following text like I'm 5 years old. Output in the same language as the source, do not output English if it is not English:": "请像解释给五岁孩子一样解释以下文本。用与源文本相同的语言输出，如果不是英语则不要输出英语:", "Please rewrite the following text to make it sound like a press release. Output in the same language as the source, do not output English if it is not English:": "请重写以下文本，使其听起来像新闻稿。用与源文本相同的语言输出，如果不是英语则不要输出英语:", "Please translate the following text to": "请将以下文本翻译成", "Please change the tone of the following text to ${tone}. Output in the same language as the source, do not output English if it is not English:": "请将以下文本的语气改为${tone}。用与源文本相同的语言输出，如果不是英语则不要输出英语:", "OK Feel free to ask me questions about your vault:": "好的，请随时就您的库向我提问：", "If you have *NEVER* as your auto-index strategy, you must click the *Refresh Index* button below, or run Copilot command: *Index vault for QA* first before you proceed!": "如果你的自动索引策略设置为 NEVER(从不) ，则必须先点击下面的 刷新索引 按钮，或者运行 Copilot 命令：\"为 QA模式 显式索引保管库(Vault)\"，然后再继续！", "Default Mode": "默认模式", "setup guide to setup Ollama's local embedding model (requires Ollama v0.1.26 or above).": "安装ollama本地嵌入模型的安装指南（需要ollama v0.1.26或更高版本）。", "mode, your vault is indexed": "模式，您的保管库(Vault)已被索引", "explicitly, or hit the ": "或者点击", " button.": "按钮。", "name: \"Indexing Exclusions\",": "name: \"索引排除项\",", "description: \"Comma separated list of paths, tags, note titles or file extension, e.g. folder1, folder1/folder2, #tag1, #tag2, [[note1]], [[note2]], *.jpg, *.excallidraw.md etc, to be excluded from the indexing process. NOTE: Tags must be in the note properties, not the note content. Files which were previously indexed will remain in the index unless you force re-index.\"": "description: \"要从索引过程中排除的路径、标签、笔记标题或文件扩展名的逗号分隔列表，例如文件夹1、文件夹1/文件夹2、#标签1、#标签2、[[笔记1]]、[[笔记2]]、*.jpg、*.excallidraw.md 等。注意: 标签必须在笔记属性中，而不是笔记内容中。先前索引的文件将保留在索引中，除非您强制重新索引。\"", ".createElement(\"h2\", null, \"Open Plugin In\")": ".createElement(\"h2\", null, \"在哪打开插件\")", ".createElement(\"option\", { value: \"view\" /* VIEW */ }, \"Sidebar View\")": ".createElement(\"option\", { value: \"view\" /* VIEW */ }, \"侧边栏\")", ".createElement(\"option\", { value: \"editor\" /* EDITOR */ }, \"Editor\")": ".createElement(\"option\", { value: \"editor\" /* EDITOR */ }, \"编辑器\")", ".createElement(\"b\", null, \"Suggested Prompts\")": ".createElement(\"b\", null, \"推荐的聊天提示词\")", "\"NEVER\" /* NEVER */": "\"NEVER(从不)\" /* NEVER */", "\"ON STARTUP\" /* ON_STARTUP */": "\"ON STARTUP(在启动时)\" /* ON_STARTUP */", "\"ON MODE SWITCH\" /* ON_MODE_SWITCH */": "\"ON MODE SWITCH(在模式切换时)\" /* ON_MODE_SWITCH */", "console.log(\"New Vault QA chain with hybrid retriever created for entire vault\")": "console.log(\"为整个保管库(Vault)创建了新的带混合检索器的 Vault QA 链\")", "\"Copilot Plus brings powerful AI agent capabilities to Obsidian. Alpha access is limited to sponsors and early supporters. Learn more at\",": "\"Copilot Plus 为 Obsidian 带来了强大的 AI 代理功能。Alpha 访问权限仅限于赞助者和早期支持者。了解更多信息请访问。\",", "title: \"License Key\",": "title: \"许可证密钥\",", "description: \"Enter your Copilot Plus license key\",": "description: \"输入您的 Copilot Plus 许可证密钥\",", "placeholder: \"Enter your license key\"": "placeholder: \"输入您的许可证密钥\"", "name: \"Suggested Prompts\",": "name: \"建议的提示词\",", "description: \"Show suggested prompts in the chat view\",": "description: \"在聊天视图中显示建议的提示词\",", "\"QA mode relies on a \"": "\"QA模式依赖于一个\"", "\"local\"), \" vector index.\"": "\"本地\"), \" 向量索引。\"", "'By \"refreshed\", it means the vault index is not rebuilt from scratch but rather updated incrementally with new/modified notes since the last index. If you need a complete rebuild, run the commands \"Clear Copilot index\" and \"Force re-index for QA\" manually. This helps reduce costs when using paid embedding models.'": "'“刷新”意味着库索引不是从头开始重建，而是根据自上次索引以来的新/修改的笔记进行增量更新。如果需要完全重建，请手动运行“清除助手索引”和“强制重新索引以进行质量检查”命令。这有助于在使用付费嵌入模型时降低费用。'", "\"New Chat\"": "\"新聊天\"", "\"(Unsaved history will be lost)\"": "\"(未保存的历史将会丢失)\"", "\"Refresh Index\"": "\"刷新索引\"", "description: \"When enabled, Copilot index won't be loaded on mobile devices to save resources. Only chat mode will be available. Any existing index from desktop sync will be preserved. Uncheck to enable QA modes on mobile.\",": "description: \"启用后，Copilot 索引将不会在移动设备上加载以节省资源。仅提供聊天模式。来自桌面的现有索引将被保留。取消选中以启用移动端的问答模式。\",", "\"Refresh Index for Vault\"": "\"刷新保管库(Vault)索引\"", "\": Notes are never indexed to the Copilot index unless users run the command \",": "\"笔记不会被索引到Copilot索引中，除非用户运行该命令。\",", "`Detected vector length: ${sampleEmbedding.length} for model: ${EmbeddingManager.getModelName(embeddingInstance)}`": "`检测到的向量长度：${sampleEmbedding.length}，模型：${EmbeddingManager.getModelName(embeddingInstance)}`", "\"Chain is not initialized properly, re-initializing chain: \",": "\"链条未正确初始化，正在重新初始化链条：\",", "\"Copilot index is not loaded. Please check your settings.\"": "\"Copilot 索引未加载。请检查您的设置。\"", "\"Verify the connection before adding the model to ensure it's properly configured and accessible.\"": "\"在添加模型之前，请先验证连接，以确认模型已正确配置且可访问。\"", "Verify Connection": "验证连接", "Add Model": "添加模型", "Notice(`Error indexing file ${file.path}. Check console for details.`)": "Notice(`索引文件 ${file.path} 时出错。请检查控制台以获取详细信息。`)", "Notice(\"Model connection verified successfully!\")": "Notice(\"模型连接验证成功！\")", "Notice(`Model verification failed: ${error.message}`)": "Notice(`模型验证失败：${error.message}`)", ".log(`${indexedFileCount} vault files indexed to Copilot index.`)": ".log(`${indexedFileCount} vault files indexed to Copilot index.`)", ".log(`${indexedFileCount} vault files re-indexed to Copilot index.`)": ".log(`${indexedFileCount} vault files re-indexed to Copilot index.`)", ".error(`Error calling tool: ${error}`)": ".error(`Error calling tool: ${error}`)", ".error(\"Embedding model ping failed:\", error2)": ".error(\"Embedding model ping failed:\", error2)", ".error(`Error indexing file ${file.path}:`, err)": ".error(`Error indexing file ${file.path}:`, err)", ".error(\"Chat model ping failed:\", error2)": ".error(\"Chat model ping failed:\", error2)", ".error(\"Error saving chat as note:\", error)": ".error(\"Error saving chat as note:\", error)", ".error(\"Model verification failed:\", error)": ".error(\"Model verification failed:\", error)", "name: \"\",\n      value: model.enabled,\n      onChange: onToggleEnabled,\n      disabled\n    }\n  )), !model.isBuiltIn && /* @__PURE__ */ import_react19.default.createElement(\"": "name: \"\",\n      value: model.enabled,\n      onChange: onToggleEnabled,\n      disabled\n    }\n  )), !model.isBuiltIn && /* @__PURE__ */ import_react19.default.createElement(\"", "name: `selected-${isEmbeddingModel ? \"embedding\" : \"chat\"}-model`": "name: `selected-${isEmbeddingModel ? \"embedding\" : \"chat\"}-model`", "name: \"\",\n      value: model.enableCors || false,\n      onChange: (value) => {\n        const updatedModels = [...activeModels];\n        updatedModels[index2].enableCors = value;\n        onUpdateModels(updatedModels);\n      }\n    }\n  )), /* @__PURE__ */ import_react19.default.createElement(\"": "name: \"\",\n      value: model.enableCors || false,\n      onChange: (value) => {\n        const updatedModels = [...activeModels];\n        updatedModels[index2].enableCors = value;\n        onUpdateModels(updatedModels);\n      }\n    }\n  )), /* @__PURE__ */ import_react19.default.createElement(\"", "name: `${name}`": "name: `${name}`", "name: \"Suggested Prompts\"": "name: \"建议提示\"", "name: \"Relevant Notes\"": "name: \"相关笔记\"", "name: \"Find relevant notes to active note\"": "name: \"查找与当前笔记相关的笔记\"", "name: \"Inspect Copilot Index by Note Paths\"": "name: \"按笔记路径检查Copilot索引\"", "name: \"List all indexed files\"": "name: \"列出所有已索引的文件\"", "name: \"Remove files from Copilot index\"": "name: \"从 Copilot 索引中移除文件\"", "description: \"Enter your Copilot Plus license key\"": "description: \"输入您的 Copilot Plus 许可证密钥\"", "text: \"Relevant Notes\"": "text: \"相关笔记\"", "text: `${item.document.title}`": "text: `${item.document.title}`", "text: `${item.document.path}`": "text: `${item.document.path}`", "text: `${metadataTexts.join(\" | \")}`": "text: `${metadataTexts.join(\" | \")}`", "[COMMAND_IDS.ADD_CUSTOM_PROMPT]: \"Add custom prompt\"": "[COMMAND_IDS.ADD_CUSTOM_PROMPT]: \"添加自定义提示\"", "[COMMAND_IDS.APPLY_ADHOC_PROMPT]: \"Apply ad-hoc custom prompt\"": "[COMMAND_IDS.APPLY_ADHOC_PROMPT]: \"应用临时自定义提示\"", "[COMMAND_IDS.APPLY_CUSTOM_PROMPT]: \"Apply custom prompt\"": "[COMMAND_IDS.APPLY_CUSTOM_PROMPT]: \"应用自定义提示\"", "[COMMAND_IDS.CHANGE_TONE]: \"Change tone of selection\"": "[COMMAND_IDS.CHANGE_TONE]: \"更改选择的语气\"", "[COMMAND_IDS.CLEAR_LOCAL_COPILOT_INDEX]: \"Clear local Copilot index\"": "[COMMAND_IDS.CLEAR_LOCAL_COPILOT_INDEX]: \"清除本地Copilot索引\"", "[COMMAND_IDS.COUNT_TOTAL_VAULT_TOKENS]: \"Count total tokens in your vault\"": "[COMMAND_IDS.COUNT_TOTAL_VAULT_TOKENS]: \"计算保管库中的总令牌数\"", "[COMMAND_IDS.COUNT_WORD_AND_TOKENS_SELECTION]: \"Count words and tokens in selection\"": "[COMMAND_IDS.COUNT_WORD_AND_TOKENS_SELECTION]: \"计算选择中的单词和令牌数\"", "[COMMAND_IDS.DELETE_CUSTOM_PROMPT]: \"Delete custom prompt\"": "[COMMAND_IDS.DELETE_CUSTOM_PROMPT]: \"删除自定义提示\"", "[COMMAND_IDS.EDIT_CUSTOM_PROMPT]: \"Edit custom prompt\"": "[COMMAND_IDS.EDIT_CUSTOM_PROMPT]: \"编辑自定义提示\"", "[COMMAND_IDS.ELI5]: \"Explain selection like I'\"": "[COMMAND_IDS.ELI5]: \"像我这样解释选择的内容\"", "[COMMAND_IDS.EMOJIFY]: \"Emojify selection\"": "[COMMAND_IDS.EMOJIFY]: \"为选择添加表情\"", "[COMMAND_IDS.FIND_RELEVANT_NOTES]: \"Find relevant notes\"": "[COMMAND_IDS.FIND_RELEVANT_NOTES]: \"查找相关笔记\"", "[COMMAND_IDS.FIX_GRAMMAR]: \"Fix grammar and spelling of selection\"": "[COMMAND_IDS.FIX_GRAMMAR]: \"修正选择的语法和拼写\"", "[COMMAND_IDS.FORCE_REINDEX_VAULT_TO_COPILOT_INDEX]: \"Force reindex vault\"": "[COMMAND_IDS.FORCE_REINDEX_VAULT_TO_COPILOT_INDEX]: \"强制重新索引保管库\"", "[COMMAND_IDS.GARBAGE_COLLECT_COPILOT_INDEX]: \"Garbage collect Copilot index (remove files that no longer exist in vault)\"": "[COMMAND_IDS.GARBAGE_COLLECT_COPILOT_INDEX]: \"垃圾回收Copilot索引（移除保管库中已不存在的文件）\"", "[COMMAND_IDS.GENERATE_GLOSSARY]: \"Generate glossary for selection\"": "[COMMAND_IDS.GENERATE_GLOSSARY]: \"为选择生成词汇表\"", "[COMMAND_IDS.GENERATE_TOC]: \"Generate table of contents for selection\"": "[COMMAND_IDS.GENERATE_TOC]: \"为选择生成目录\"", "[COMMAND_IDS.INDEX_VAULT_TO_COPILOT_INDEX]: \"Index (refresh) vault\"": "[COMMAND_IDS.INDEX_VAULT_TO_COPILOT_INDEX]: \"索引（刷新）保管库\"", "[COMMAND_IDS.INSPECT_COPILOT_INDEX_BY_NOTE_PATHS]: \"Inspect Copilot index by note paths (debug)\"": "[COMMAND_IDS.INSPECT_COPILOT_INDEX_BY_NOTE_PATHS]: \"按笔记路径检查Copilot索引（调试）\"", "[COMMAND_IDS.LIST_INDEXED_FILES]: \"List all indexed files (debug)\"": "[COMMAND_IDS.LIST_INDEXED_FILES]: \"列出所有已索引的文件（调试）\"", "[COMMAND_IDS.LOAD_COPILOT_CHAT_CONVERSATION]: \"Load Copilot chat conversation\"": "[COMMAND_IDS.LOAD_COPILOT_CHAT_CONVERSATION]: \"加载Copilot聊天对话\"", "[COMMAND_IDS.MAKE_LONGER]: \"Make selection longer\"": "[COMMAND_IDS.MAKE_LONGER]: \"使选择变长\"", "[COMMAND_IDS.MAKE_SHORTER]: \"Make selection shorter\"": "[COMMAND_IDS.MAKE_SHORTER]: \"使选择变短\"", "[COMMAND_IDS.OPEN_COPILOT_CHAT_WINDOW]: \"Open Copilot Chat Window\"": "[COMMAND_IDS.OPEN_COPILOT_CHAT_WINDOW]: \"打开Copilot聊天窗口\"", "[COMMAND_IDS.PRESS_RELEASE]: \"Rewrite selection to a press release\"": "[COMMAND_IDS.PRESS_RELEASE]: \"将选择改写为新闻稿\"", "[COMMAND_IDS.REMOVE_FILES_FROM_COPILOT_INDEX]: \"Remove files from Copilot index (debug)\"": "[COMMAND_IDS.REMOVE_FILES_FROM_COPILOT_INDEX]: \"从Copilot索引中移除文件（调试）\"", "[COMMAND_IDS.REMOVE_URLS]: \"Remove URLs from selection\"": "[COMMAND_IDS.REMOVE_URLS]: \"从选择中移除URL\"", "[COMMAND_IDS.REWRITE_TWEET]: \"Rewrite selection to a tweet\"": "[COMMAND_IDS.REWRITE_TWEET]: \"将选择改写为推文\"", "[COMMAND_IDS.REWRITE_TWEET_THREAD]: \"Rewrite selection to a tweet thread\"": "[COMMAND_IDS.REWRITE_TWEET_THREAD]: \"将选择改写为推文线程\"", "[COMMAND_IDS.SEARCH_ORAMA_DB]: \"Search OramaDB (debug)\"": "[COMMAND_IDS.SEARCH_ORAMA_DB]: \"搜索OramaDB（调试）\"", "[COMMAND_IDS.SIMPLIFY]: \"Simplify selection\"": "[COMMAND_IDS.SIMPLIFY]: \"简化选择\"", "[COMMAND_IDS.SUMMARIZE]: \"Summarize selection\"": "[COMMAND_IDS.SUMMARIZE]: \"总结选择\"", "[COMMAND_IDS.TOGGLE_COPILOT_CHAT_WINDOW]: \"Toggle Copilot Chat Window\"": "[COMMAND_IDS.TOGGLE_COPILOT_CHAT_WINDOW]: \"切换Copilot聊天窗口\"", "[COMMAND_IDS.TRANSLATE]: \"Translate selection\"": "[COMMAND_IDS.TRANSLATE]: \"翻译选择\"", "\"Relevance is a combination of semantic similarity and links.\"": "\"相关性是语义相似性和链接的结合。\"", "\"No relevant notes found\"": "\"未找到相关笔记\"", "\"Relevant Notes\"": "\"相关笔记\"", "\"Suggested Prompts\"": "\"建议提示\"", "\"Failed to initialize vector store. Please make sure you have a valid API key for your embedding model and restart the plugin.\"": "\"无法初始化向量存储。请确保您为嵌入模型提供了有效的API密钥，并重新启动插件。\"", "name: \"Open Copilot Chat Window\"": "name: \"打开 Copilot 聊天窗口\"", "name: \"Clear Copilot index\"": "name: \"清除 Copilot 索引\"", "name: \"Garbage collect Copilot index (remove files that no longer exist in vault)\"": "name: \"垃圾回收 Copilot 索引（移除保险库中不再存在的文件）\"", "Notice(`Error indexing file ${filePath || \"unknown\"}. Check console for details.`)": "Notice(`Error indexing file ${filePath || \"unknown\"}. Check console for details.`)", "Notice(\n            \"Failed to initialize vector store. Please make sure you have a valid API key for your embedding model and restart the plugin.\"\n          )": "Notice(\n            \"无法初始化向量存储。请确保您拥有有效的嵌入模型API密钥，并重新启动插件。\"\n          )", "Notice(\"Database not found\")": "Notice(\"数据库未找到\")", "Notice(\"Error executing search. Check console for details.\")": "Notice(\"执行搜索时出错。请检查控制台以获取详细信息。\")", "Notice(\"Copied to clipboard\")": "Notice(\"已复制到剪贴板\")", "Notice(`Selected text contains ${wordCount} words and ${tokenCount} tokens.`)": "Notice(`Selected text contains ${wordCount} words and ${tokenCount} tokens.`)", "Notice(\"An error occurred while counting tokens.\")": "Notice(\"An error occurred while counting tokens.\")", "Notice(\n          \"API key verification failed: No default test model found for the selected provider.\"\n        )": "Notice(\n          \"API key verification failed: No default test model found for the selected provider.\"\n        )", "Notice(\"API key verified successfully!\")": "Notice(\"API key verified successfully!\")", "Notice(\"API key verification failed: \" + err2String(error)": "Notice(\"API key verification failed: \" + err2String(error)", "Notice(`Error: Missing required variables: ${missingVars.join(\", \")": "Notice(`Error: Missing required variables: ${missingVars.join(\", \")", "Notice(`Error: Format contains illegal characters (\\\\/:*?\"<>|)": "Notice(`Error: Format contains illegal characters (\\\\/:*?\"<>|)", "Notice(`Format applied successfully! Example: ${customFileName}`, 4e3)": "Notice(`Format applied successfully! Example: ${customFileName}`, 4e3)", "Notice(`Error applying format: ${error.message}`, 4e3)": "Notice(`Error applying format: ${error.message}`, 4e3)", "Notice(\"Please fill in all required fields\")": "Notice(\"Please fill in all required fields\")", "Notice(\"Model verification successful!\")": "Notice(\"Model verification successful!\")", "Notice(\"Model verification failed: \" + errStr)": "Notice(\"Model verification failed: \" + errStr)", ".log(\"No chunks found for query:\", query)": ".log(\"No chunks found for query:\", query)", ".log(\"==== Orama Search Params: ====\\n\", searchParams)": ".log(\"==== Orama Search Params: ====\\n\", searchParams)", ".log(\"returnAll:\", returnAll)": ".log(\"returnAll:\", returnAll)", ".log(\"Truncating documents to fit context length. Truncation ratio:\", truncationRatio)": ".log(\"Truncating documents to fit context length. Truncation ratio:\", truncationRatio)", "alog();\n    }\n  }, [rootRef, openFileDialog])": "alog();\n    }\n  }, [rootRef, openFileDialog])", "alog();\n    }\n  }, [noClick, openFileDialog])": "alog();\n    }\n  }, [noClick, openFileDialog])", ".log(`Verifying ${provider} API key:`, apiKey)": ".log(`Verifying ${provider} API key:`, apiKey)", "alog(true)": "alog(true)", ".error(message)": ".error(message)", ".error(\"Encryption failed:\", error)": ".error(\"Encryption failed:\", error)", ".error(\"Error checking if URL is image:\", error)": ".error(\"Error checking if URL is image:\", error)", ".error(\"Error converting image to base64:\", error)": ".error(\"Error converting image to base64:\", error)", ".error(\"safeFetch error:\", error)": ".error(\"safeFetch error:\", error)", ".error(`Error calling tool:`, error)": ".error(`Error calling tool:`, error)", ".error(\"Failed to initialize database during save:\", error)": ".error(\"Failed to initialize database during save:\", error)", ".error(`Error indexing file ${filePath || \"unknown\"}:`, err)": ".error(`Error indexing file ${filePath || \"unknown\"}:`, err)", ".error(\"Error processing image URLs:\", error)": ".error(\"Error processing image URLs:\", error)", ".error(\"Error processing images:\", error)": ".error(\"Error processing images:\", error)", ".error(\"Error processing YouTube video:\", error)": ".error(\"Error processing YouTube video:\", error)", ".error(\"Error in debug search:\", error)": ".error(\"Error in debug search:\", error)", ".error(\"Error saving chat as note:\", err2String(error)": ".error(\"Error saving chat as note:\", err2String(error)", ".error(\"Failed to decrypt value:\", error)": ".error(\"Failed to decrypt value:\", error)", ".error(MESSAGE)": ".error(MESSAGE)", ".error(\"API key verification failed:\", error)": ".error(\"API key verification failed:\", error)", "name: \"copilot-plus-large\"": "name: \"copilot-plus-large\"", "name: \"AES-GCM\"": "name: \"AES-GCM\"", "name: \"\",\n      provider,\n      enabled: true,\n      isBuiltIn: false,\n      baseUrl: \"": "name: \"\",\n      provider,\n      enabled: true,\n      isBuiltIn: false,\n      baseUrl: \"", "description: \"Files\"": "description: \"Files\"", "description: \"Select the Chat model to use\"": "description: \"Select the Chat model to use\"", "description: \"Choose where to open the plugin\"": "description: \"Choose where to open the plugin\"", "description: \"Enable or disable builtin Copilot commands\"": "description: \"Enable or disable builtin Copilot commands\"", "description: \"Enter OpenAI Organization ID if applicable\"": "description: \"Enter OpenAI Organization ID if applicable\"", "description: \"Leave it blank, unless you are using a proxy.\"": "description: \"Leave it blank, unless you are using a proxy.\"", "description: \"Enable encryption for the API keys.\"": "description: \"Enable encryption for the API keys.\"", "description: \"Debug mode will log some debug message to the console.\"": "description: \"Debug mode will log some debug message to the console.\"", "description: \"When specified, ONLY these paths, tags, or note titles will be indexed (comma separated). Takes precedence over exclusions. Files which were previously indexed will remain in the index unless you force re-index. Format: folder1, folder1/folder2, #tag1, #tag2, [[note1]], [[note2]]\"": "description: \"When specified, ONLY these paths, tags, or note titles will be indexed (comma separated). Takes precedence over exclusions. Files which were previously indexed will remain in the index unless you force re-index. Format: folder1, folder1/folder2, #tag1, #tag2, [[note1]], [[note2]]\"", "link: \"text-accent underline-offset-4 hover:underline\"": "link: \"text-accent underline-offset-4 hover:underline\"", "text: \"Debug: Search OramaDB\"": "text: \"Debug: Search OramaDB\"", "text: \"Search\"": "text: \"Search\"", "search:\", error);\n        new import_obsidian16.Notice(\"": "search:\", error);\n        new import_obsidian16.Notice(\"", ".setTitle(`Copilot: ${COMMAND_NAMES[commandId]}`)": ".setTitle(`Copilot: ${COMMAND_NAMES[commandId]}`)", "label: \"label\"": "label: \"label\"", "label: \"OpenAI\"": "label: \"OpenAI\"", "label: \"Azure OpenAI\"": "label: \"Azure OpenAI\"", "label: \"Anthropic\"": "label: \"Anthropic\"", "label: \"Cohere\"": "label: \"Cohere\"", "label: \"Gemini\"": "label: \"Gemini\"", "label: \"OpenRouter\"": "label: \"OpenRouter\"", "label: \"Groq\"": "label: \"Groq\"", "label: \"Ollama\"": "label: \"Ollama\"", "label: \"LM Studio\"": "label: \"LM Studio\"", "label: \"OpenAI Format\"": "label: \"OpenAI Format\"", "label: \"Copilot Plus\"": "label: \"Copilot Plus\"", "label: `${model.name} (${getProviderLabel(model.provider)})`": "label: `${model.name} (${getProviderLabel(model.provider)})`", "label: \"Sidebar View\"": "label: \"Sidebar View\"", "label: \"Editor\"": "label: \"Editor\"", "label: \"OpenAI Organization ID\"": "label: \"OpenAI Organization ID\"", "label: \"Instance Name\"": "label: \"Instance Name\"", "label: \"Deployment Name\"": "label: \"Deployment Name\"", "label: \"Embedding Deployment Name\"": "label: \"Embedding Deployment Name\"", "label: \"API Version\"": "label: \"API Version\"", "label: \"Model Name\"": "label: \"Model Name\"", "label: \"Provider\"": "label: \"Provider\"", "label: \"Base URL\"": "label: \"Base URL\"", "label: \"API Key\"": "label: \"API Key\"", "title: \"title\"": "title: \"title\"", "title: \"xlinkTitle\"": "title: \"xlinkTitle\"", "title: \"string\"": "title: \"string\"", "title: \"New Chat\"": "title: \"New Chat\"", "title: \"Save Chat as Note\"": "title: \"<PERSON> Cha<PERSON> as Note\"", "title: \"Advanced Settings\"": "title: \"Advanced Settings\"", "title: \"Remove image\"": "title: \"Remove image\"", "title: \"Delete\"": "title: \"Delete\"", "title: \"Show Sources\"": "title: \"Show Sources\"", "title: \"Active Note Insights\"": "title: \"Active Note Insights\"", "title: \"Note Link Chat\"": "title: \"<PERSON>\"", "title: \"Test LLM\"": "title: \"Test LLM\"", "title: \"Vault Q&A\"": "title: \"Vault Q&A\"", "title: \"Copilot Plus\"": "title: \"Copilot Plus\"", "title: \"License Key\"": "title: \"License Key\"", "title: \"API Keys\"": "title: \"API Keys\"", "title: \"Default Chat Model\"": "title: \"De<PERSON>ult <PERSON>\"", "title: \"Embedding Model\"": "title: \"Embedding Model\"", "title: \"Default Mode\"": "title: \"Default Mode\"", "title: \"Open Plugin In\"": "title: \"Open Plugin In\"", "title: \"Default Conversation Folder Name\"": "title: \"Default Conversation Folder Name\"", "title: \"Custom Prompts Folder Name\"": "title: \"Custom Prompts Folder Name\"", "title: \"Default Conversation Tag\"": "title: \"Default Conversation Tag\"", "title: \"Conversation Filename Template\"": "title: \"Conversation Filename Template\"", "title: \"Autosave Chat\"": "title: \"<PERSON><PERSON>ve <PERSON>\"", "title: \"Suggested Prompts\"": "title: \"Suggested Prompts\"", "title: \"Relevant Notes\"": "title: \"Relevant Notes\"", "title: \"Command Settings\"": "title: \"Command Settings\"", "title: \"Chat Model\"": "title: \"Chat Model\"", "title: \"Temperature\"": "title: \"Temper<PERSON>\"", "title: \"Token limit\"": "title: \"Token limit\"", "title: \"Conversation turns in context\"": "title: \"Conversation turns in context\"", "title: \"User System Prompt\"": "title: \"User System Prompt\"", "title: \"Enable Encryption\"": "title: \"Enable Encryption\"", "title: \"Debug Mode\"": "title: \"Debug Mode\"", "title: \"Auto-Index Strategy\"": "title: \"Auto-Index Strategy\"", "title: \"Max Sources\"": "title: \"Max Sources\"", "title: \"Requests per second\"": "title: \"Requests per second\"", "title: \"Number of Partitions\"": "title: \"Number of Partitions\"", "title: \"Exclusions\"": "title: \"Exclusions\"", "title: \"Inclusions\"": "title: \"Inclusions\"", "title: \"Enable Obsidian Sync for Copilot index\"": "title: \"Enable Obsidian Sync for Copilot index\"", "title: \"Disable index loading on mobile\"": "title: \"Disable index loading on mobile\"", "prompts: [\n      `Provide three follow-up questions worded as if I'm asking you based on {activeNote}?`,\n      `What key questions does {activeNote} answer?`,\n      `Give me a quick recap of {activeNote} in two sentences.`\n    ]": "prompts: [\n      `Provide three follow-up questions worded as if I'm asking you based on {activeNote}?`,\n      `What key questions does {activeNote} answer?`,\n      `Give me a quick recap of {activeNote} in two sentences.`\n    ]", "prompts: [\n      `Based on [[<note>]": "prompts: [\n      `Based on [[<note>]", "prompts: [\n      `9.11 and 9.8, which is bigger?`,\n      `What's the longest river in the world?`,\n      `If a lead ball and a feather are dropped simultaneously from the same height, which will reach the ground first?`\n    ]": "prompts: [\n      `9.11 and 9.8, which is bigger?`,\n      `What's the longest river in the world?`,\n      `If a lead ball and a feather are dropped simultaneously from the same height, which will reach the ground first?`\n    ]", "prompts: [\n      `What insights can I gather about <topic> from my notes?`,\n      `Explain <concept> based on my stored notes.`,\n      `Highlight important details on <topic> from my notes.`,\n      `Based on my notes on <topic>, what is the question that I should be asking, but am not?`\n    ]": "prompts: [\n      `What insights can I gather about <topic> from my notes?`,\n      `Explain <concept> based on my stored notes.`,\n      `Highlight important details on <topic> from my notes.`,\n      `Based on my notes on <topic>, what is the question that I should be asking, but am not?`\n    ]", "text = \"\\u{1F534} Low Similarity\"": "text = \"🔴 低相似\"", "\"\\u{1F7E0} Medium Similarity\"": "\"🟠 中相似\"", "\"\\u{1F7E2} High Similarity\"": "\"🟢 高相似\""}}